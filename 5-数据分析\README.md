# 数据分析系统 - MD文件版

## 🎯 项目简介

基于数据分层处理理念的股市复盘数据管理系统，实现原始数据验证+AI智能分析的双层模式。

**设计理念**：数据透明可验证，分析智能有价值

## ⚡ 快速开始

### 1. 数据录入
```
📸 上传复盘图片 → 🤖 AI识别 → 📝 生成原始识别.md → 👀 用户验证修正
```

### 2. 数据分析
```
📊 多天原始数据 → 🤖 AI深度分析 → 📝 生成碎片笔记/股市复盘.md
```

### 3. 使用示例
- **录入**："帮我识别这张复盘图片，生成原始识别.md"
- **分析**："基于最近5天的原始识别数据，生成本周复盘分析"
- **查询**："查看6月20日的市场数据"

## 📁 目录结构

```
5-数据分析/
├── 原始录入/                    # AI识别的原始数据（用户验证后）
│   ├── 2025-06-26-原始识别.md
│   ├── 2025-06-27-原始识别.md
│   ├── 模板-原始识别.md         # 标准格式模板
│   └── README.md
├── 人气异动/                    # 人气排名异动分析
├── 分析报告/                    # 生成的分析报告
└── README.md                   # 本文档

1-输入/2-碎片笔记/
├── 股市复盘-2025-06-26.md      # AI分析后的深度洞察
└── ...
```

## 🛠️ 核心功能

### 📊 data_input.py - 数据录入
- 日度复盘数据录入
- 龙虎榜数据录入
- 空间梯队数据录入
- 人气排名数据录入

### 🔍 data_query.py - 数据查询
- 复盘数据查询
- 龙虎榜数据查询
- 梯队数据查询
- 人气排名查询
- 数据库统计

### 🔧 quality_check.py - 质量检查
- 格式统一性检查
- 计算准确性检查
- 数据类型检查
- 完整性检查
- 自动修复功能

### 📈 report_generator.py - 报告生成
- 日度复盘报告
- 周度综合分析
- 题材专题分析

## 📋 使用流程

### 日常复盘
1. **录入** → 2. **检查** → 3. **报告** → 4. **查询**

### 周度分析
1. **周度报告** → 2. **题材分析**

## ✨ 系统优势

- **🎯 极简设计**：4个工具，功能清晰
- **⚡ 开箱即用**：无复杂配置，立即可用
- **🔒 数据可靠**：完整的质量保障机制
- **📊 报告丰富**：多种分析维度
- **🛠️ 易于维护**：最小化依赖，便于扩展

## 📋 数据录入规范

**板数格式**：1,2,3,4,5,6,7（数字），反包股标注为"反包"
**金额单位**：万元
**概念分隔**：多个概念用"|"分隔  
**日期格式**：YYYY-MM-DD

## 🔄 版本历史

### v1.0 (2025-06-25)
- ✅ 初始版本发布
- ✅ 4个核心工具完成
- ✅ 数据质量保障机制
- ✅ 完整文档体系

## 💡 设计哲学

> "简单的才是高效的"

这个系统从50+个复杂文件简化为4个核心工具，专注于：
- **核心功能**：只保留最重要的功能
- **用户体验**：简单易用，无学习成本
- **数据质量**：确保数据准确可靠
- **维护成本**：最小化配置和依赖

## 🎖️ 适用场景

- 个人股市复盘分析
- 小团队数据管理
- 学习数据分析方法
- 快速原型开发

---

**开发理念**：Less is More
**版本**：简洁版 v1.0
**更新时间**：2025-06-25 