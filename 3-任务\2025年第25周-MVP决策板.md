# MVP优先决策板（周次：2025年第25周）

> **创建时间**: 2025-06-22 09:17:15  
> **状态**: 🔄 进行中  
> **完成度**: 0%

## 1. 核心 20% 高价值任务

本周实际完成后能释放最大用户价值的极少数任务（最多 3–5 项）。

| 序号  | 任务  | 痛点评分¹ | 收益评分² | 优先级 (痛点×收益) | 状态  | 完成度 |
| --- | --- | ----- | ----- | ----------- | --- | --- |
| 1   |     |       |       |             | ⏳   | 0%  |
| 2   |     |       |       |             | ⏳   | 0%  |
| 3   |     |       |       |             | ⏳   | 0%  |
| 4   |     |       |       |             | ⏳   | 0%  |
| 5   |     |       |       |             | ⏳   | 0%  |

**评分说明**：
- ¹ **痛点评分**：该任务解决的用户痛点程度（1 = 可忽略，10 = 关键）
- ² **收益评分**：若本周解决该任务，可带来的业务/学习收益（1 = 低，10 = 高）

**状态图例**：
- ⏳ 待开始  
- 🔄 进行中  
- ✅ 已完成  
- ❌ 已取消  
- ⏸️ 暂停

---

## 2. 时间分配

**总专注小时数**: 40 小时

| 任务 # | 任务名称 | 预估耗时 (小时) | 负责人 | 截止日期 | 实际耗时 |
| ---- | ------ | --------- | --- | ---- | ------ |
| 1    |        |           | dabo228 |      |        |
| 2    |        |           | dabo228 |      |        |
| 3    |        |           | dabo228 |      |        |
| 4    |        |           | dabo228 |      |        |
| 5    |        |           | dabo228 |      |        |

**时间分配原则**：
- 🎯 高优先级任务分配更多时间
- ⚡ 预留20%缓冲时间应对突发情况
- 📅 合理分散任务，避免集中在周末

---

## 3. 阻碍因素与依赖

### 🚧 潜在阻碍
- [ ] 外部依赖：
- [ ] 资源限制：
- [ ] 技能缺口：
- [ ] 时间冲突：

### 🔗 关键依赖
- [ ] 需要他人配合：
- [ ] 等待外部反馈：
- [ ] 依赖工具/系统：

### 💡 解决方案
- [ ] 备选方案：
- [ ] 风险缓解：
- [ ] 提前准备：

---

## 4. 周中检查 (2025-06-19)

### 📊 进度回顾
- **已完成任务**: 
- **进行中任务**: 
- **遇到的问题**: 
- **需要调整的计划**: 

### 🔄 计划调整
- [ ] 任务优先级调整：
- [ ] 时间分配调整：
- [ ] 新增紧急任务：
- [ ] 延期任务：

---

## 5. 周末总结 (2025-06-22)

### ✅ 完成情况
- **完成任务数**: ___/5
- **总体完成度**: ___%
- **实际专注时间**: ___ 小时

### 📈 收获与成长
- **主要成果**: 
- **学到的经验**: 
- **改进的技能**: 

### 🔍 反思与优化
- **做得好的地方**: 
- **需要改进的地方**: 
- **下周优化建议**: 

---

## 6. 数据统计

| 指标 | 计划值 | 实际值 | 达成率 |
| -- | ---- | ---- | ---- |
| 任务完成数 | 5 | ___ | ___% |
| 专注时间 | 40 | ___ | ___% |
| 高优先级任务完成 | 3 | ___ | ___% |

---

> **💡 提示** — 坚持 80/20 原则：若某项任务优先级未进前三，请考虑延期或委派。

> **📝 归档说明** — 周末完成后，请将此文件移动到 `archive/` 目录，并更新状态为"已完成"。
