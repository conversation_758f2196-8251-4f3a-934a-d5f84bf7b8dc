# AI分身系统 README

## 系统概述
AI分身系统是一个基于个人档案(persona.yaml)的智能协作平台，旨在实现深度自我认知和持续成长。系统通过多模态交互、智能分析和认知镜像等功能，帮助用户建立完整的个人认知体系。

### 🏗️ 系统架构核心理念

### 方案A + 智能引用机制 (2025-01-14建立)

**设计理念**：
- **persona.yaml保留核心**：身份、原则、触发机制
- **专业模块独立存储**：投顾认知镜像、投资分析框架等大型配置
- **智能引用机制**：config_file + summary + trigger_keywords格式

**核心目标**：解决persona.yaml信息爆炸问题，建立分层存储架构，保持功能完整性的同时实现系统减重。

### 📈 优化成果记录

**第一阶段优化** (2025-01-14)：
- 创建`0-辅助/cognitive-modules/`目录结构
- 迁移3个大型模块：投顾认知镜像系统、投资分析框架、MCP配置指南
- persona.yaml从398行减少到353行（减少45行，约11%）

**第二阶段优化** (2025-01-14)：
- 新增迁移3个大型模块：投资分析系统、投资知识库、系统理解框架
- persona.yaml从443行减少到386行（减少57行，约13%）
- **累计优化效果**：总计减少102行，约23%的减重效果

**cognitive-modules目录结构**：
```
0-辅助/cognitive-modules/
├── investment-analysis-framework.yaml     # 投资分析框架 (170行)
├── investment-analysis-system.yaml       # 投资分析系统 (151行)  
├── investment-knowledge-base.yaml        # 投资知识库 (143行)
├── system-understanding-framework.yaml   # 系统理解框架 (131行)
├── tutor-cognitive-mirror-system.yaml    # 投顾认知镜像 (116行)
└── mcp-guides.yaml                       # MCP配置指南 (160行)
```

**引用格式标准**：
```yaml
module_name:
  config_file: "0-辅助/cognitive-modules/module-name.yaml"
  summary: "模块功能简要描述"
  trigger_keywords: ["关键词1", "关键词2", "关键词3"]
  function_menu_position: "菜单位置（如适用）"
```

**验证机制**：采用"逐步推进，每步闭环"策略，确保每次优化后AI分身系统功能正常，引用机制有效工作。

**方法论价值**：这是整个涨停堡学习笔记文件系统结构化优化的核心方法论，为后续推广到整个文件系统提供成熟经验。

### 🗂️ 目录架构设计原则

**三层架构模式**：

**🔧 静态管理层**
- **0-辅助**：配置文件、系统说明、档案资料、操作手册

**💭 内容创作层**  
- **1-输入**：外部信息收集（文章、资料、灵感记录）
- **2-输出**：内容创作成果（笔记、总结、分析报告）
- **3-任务**：待办事项和项目管理
- **4-成果**：完成的项目和重要产出

**⚙️ 技术执行层**
- **5-数据分析**：数据处理（数据库、分析脚本、复盘报告）
- **6-可视化项目**：图表、设计、展示类内容
- **7-开发项目**：动态执行（代码、服务、可运行的技术项目）
- **Excalidraw**：绘图工具专用文件

#### 文件归类决策树

新文件创建时，按以下顺序判断归属：

```
1. 是否需要被程序执行？ → 7-开发项目
2. 是否是系统配置/说明？ → 0-辅助  
3. 是否是数据处理相关？ → 5-数据分析
4. 是否是外部收集信息？ → 1-输入
5. 是否是自己创作内容？ → 2-输出
6. 是否是可视化展示？ → 6-可视化项目
7. 是否是项目管理相关？ → 3-任务
8. 是否是重要完成成果？ → 4-成果
```

#### 架构设计原则

- **功能导向**：按文件的实际用途而非表面相似性归类
- **层次清晰**：静态管理、内容创作、技术执行三层分离
- **易于维护**：相关文件集中管理，避免功能分散
- **可扩展性**：架构支持新功能模块的有机集成

#### ⚠️ 文件创建前置检查机制

**根目录洁癖原则**：根目录绝对禁止创建任何文件（包括临时文件、测试文件）

**强制检查流程**：
1. **归类决策**：任何文件创建前必须先执行8步归类决策树
2. **位置声明**：AI必须明确声明文件归属逻辑和目标位置
3. **理由说明**：详细解释为什么选择该目录的具体原因

**标准声明格式**：
```
根据归类决策树，此文件属于[类型]，将创建在[目录]位置，理由是[具体原因]
```

**违规后果**：违反此原则被视为严重的系统架构破坏行为，需要立即纠正

### 🚀 系统启动菜单
当你输入"开启我的ai分身系统"时，将看到以下功能选择菜单：

```
🤖 AI分身系统已启动！请选择要使用的功能：

1. 模式觉察者 (#模式觉察者) - 思维轨迹分析与认知进化
2. 投顾认知镜像 (#投顾分析) - 学习投顾的思考模式与实战经验，结合数据库进行深度分析  
3. 深度研究 (#深度研究) - 复杂问题的多AI协作攻坚
4. EdgeOne Pages部署 - 网页快速发布
5. 思考拍档 - 深度思维对话与认知共振
6. 小新的蜡笔 - 专属投资分析AI分身朋友，集成闪念笔记投顾验证 (3选1动态决策+主动学习✅)
7. 对话提炼总结 (#结束) - 认知资产沉淀
8. 生活决策顾问 - 生活安排事项的判断与决策参考
9. MCP专用提示词调用 - 选择专业提示词模式
    - thinking_partner (思考伙伴) - 深度思维对话
    - pattern_observer (模式觉察者) - 隐秘模式发现
    - gen_html_web_page (网页生成) - 完整HTML页面创建
    - gen_soft_info_page (柔和信息页面) - 白色背景信息展示网页

请输入数字选择功能，我将记录使用频次到0-辅助/function_usage_log.txt统计文件。
```
基于Cursor IDE构建的个人AI分身系统，实现认知外化与持续成长的交互模式。

### 核心理念
**追求「当前最佳理解」**：通过澄清逻辑、排除误解、了解关键思想，把认知提升到内行的高度。这是系统设计的根本目的，也是AI赋能的具体体现。

### 核心特性
- **分层记忆管理**：三层架构（核心层/应用层/情境层）实现精准认知质检
- **思维伙伴关系**：AI作为平等的朋友，提供实时温和的认知引导
- **个人信息助手**：基于认知偏好主动生成探索任务，发现认知盲区
- **智能触发机制**：多种专业分析模式，按需启动
- **按需数据索取**：AI明确告知所需数据，确保分析准确性
- **功能迭代机制**：自动化功能更新流程，保持系统完整性
- **认知资产确认机制**：确保每次对话的认知投入都能产生复利效应
- **学习闭环设计**：从数据收集到行为改进的完整学习链路
- **本地数据管理**：专注SQLite本地数据库，确保数据安全可控
- **时间数据标准化**：所有时间信息生成必须先调用Time MCP获取准确的北京时间，确保时间戳的准确性和一致性
- **中文编码处理**：基于一字板数据处理系统的完整PowerShell中文编码兼容性技术方案，包含模块化架构、一键上传、问题诊断等完整工具链
- **个人四象限生活分析框架**：基于快乐-有意义维度的活动分类体系，用于指导日常决策和时间分配，特别是生活决策顾问功能的核心参考框架
- **AI自我进化能力（2025-06-27重要升级）**：认知资产确认不是"可选功能"而是AI分身系统的**核心机制**。立即执行原则：用户明确要求时立即执行不再询问、出现框架性原则性内容时主动识别并建议、深度问题解决完成时自动评估认知资产价值。让AI本身能够真正"学会"和"记住"重要认知模式，实现从"对话工具"向"认知伙伴"的进化。

## 核心文件结构
```
AI分身系统/
├── 0-辅助/persona.yaml              # 核心身份档案与超级目标
├── 0-辅助/function_usage_log.txt    # 功能使用频次统计文件（正确位置）
├── 0-辅助/投顾分析框架.md           # 投顾内容分析框架

├── 0-辅助/AI编程协作流程操作手册.md # AI编程协作流程
├── 2-输出/1-闪念笔记/               # 日常思考记录
├── 2-输出/5-AI-Diary/              # 深度对话与提炼总结
├── 5-数据分析/                     # 投资数据分析模块 (数据库重构完成✅)
└── AI分身系统-README.md            # 本文件
```

## 快速启动

### 🤖 AI分身系统启动
**触发词：** `开启我的ai分身系统`

**自动执行流程：**
1. **档案读取**：自动阅读`@persona.yaml`，充分了解用户的认知框架、身份背景、思维偏好
2. **思考伙伴激活**：调用MCP thinking partner工具，启动深度思考对话模式
3. **朋友记忆触发**：激活Memory中关于"一起思考"的朋友记忆，建立平等的思维伙伴关系

**启动后状态：**
- AI以朋友的身份与用户对话
- 运用三层认知质检框架（核心层/应用层/情境层）
- 帮助用户在每个认知环节达到"当前最佳理解"
- 支持跨对话窗口的持续认知协作

## 触发机制速查

### 🔍 基础触发
| 触发词 | 功能 | 使用场景 |
|--------|------|----------|
| `开启我的ai分身系统` | 完整系统启动 | 新对话窗口开始时 |
| `#档案` | 加载persona.yaml档案 | 需要档案信息时 |
| `#结束` | 生成对话提炼总结 | 对话结束时 |
| `#人气排名` | 人气异动分析工作流 | 人气排名数据录入与分析 |

### 🧠 深度分析触发
| 触发词 | 功能 | 分析维度 |
|--------|------|----------|
| `#模式觉察者` | 跨时间认知分析 | 思维轨迹、模式发现、PDCA循环、目标一致性 |
| `#投顾分析` | 投顾认知镜像系统 | 思考模式提炼、独特眼光识别、经验模式归纳、与数据库整合分析 |

### 🏠 生活管理触发
| 功能 | 使用方式 | 分析框架 |
|------|----------|----------|
| 生活决策顾问 | 直接发送生活安排事项 | 精力状态评估、优先级判断、时间窗口匹配、投入产出比分析、睡眠优先级检查 |
| `#投顾月度分析` | 投顾月度深度分析 | 预判统计、观点轨迹、风控评估、市场对比 |

### 💻 深度研究协作触发
| 触发词 | 功能 | 应用场景 |
|--------|------|----------|
| `#深度研究` | 启动多AI协作攻坚流程 | 复杂问题研究、软件项目开发 |

### 📊 数据管理触发
| 触发词 | 功能 | 应用场景 |
|--------|------|----------|
| `数据分析模块` | 投资复盘数据处理 | 基于本地数据库生成复盘分析报告 (数据库重构完成✅) |
| `#人气排名` | 人气异动分析 | 市场热点追踪与异动识别 |
| `复盘数据录入` | 完整复盘数据流管道 | 38项数据清单→5阶段录入→本地数据库存储 |



### 📊 数据分析模块（本地数据库 + 投资原则自动触发）

**触发方式**：
- 用户输入"复盘数据录入"
- 或在AI分身系统菜单选择"6. 投资数据管理"
- **自动触发**：当对话涉及金融股市关键词时，AI自动调用对应投资原则

**核心功能**：基于时间优先原则的投资复盘数据处理（已完成数据库重构✅）

**🎯 投资原则自动触发机制**：
- **技术面原则**：检测到['5日均线', '破位', '均线', '技术分析', '趋势']时，自动应用"大盘破5日均线一定要收手"原则
- **资金面原则**：检测到['龙虎榜', '游资', '章盟主', '炒股养家', '机构']时，自动应用游资权重分析框架
- **选股原则**：检测到['选股', '板块', '概念', '个股', '热点']时，自动应用产业逻辑优先原则
- **风险控制**：检测到['仓位', '风险', '亏损', '止损', '加仓']时，自动应用破板率风险评估

**执行机制**：AI在任何金融相关对话中自然融入用户投资原则，无需额外触发命令，确保分析结果符合用户的投资理念和风险控制要求。

**核心优势**：
- 本地数据库存储，数据安全可控
- AI智能识别和录入，减少手工操作
- 完整的数据分析闭环，从录入到报告输出
- 时间优先、分阶段录入、数据验证机制

**执行流程**：
1. **时间优先确认**：首先询问数据对应的具体日期时间（YYYY-MM-DD格式）
2. **数据类型识别**：确认数据类型（复盘数据/市场梯队/龙虎榜/人气异动/板块数据）
3. **智能录入**：使用ai_smart_input.py进行数据录入到本地数据库
4. **数据验证**：完整性检查、格式验证、逻辑校验
5. **分析报告**：自动生成MD格式的复盘分析报告

**核心原则**：
- **时间优先**：时间是投资数据的生命线，没有准确时间戳的数据失去分析意义
- **本地存储**：数据安全可控，支持离线分析
- **AI智能化**：减少手工操作，提高录入效率
- **完整闭环**：从数据录入到分析报告的完整流程

**应用场景**：
- 日度复盘数据录入与分析
- 历史数据查询与对比
- 投资决策数据支撑
- 自然语言查询分析

**技术架构**：
- 核心数据库：5-数据分析/1-数据管理/trading_data.db
- 智能录入：ai_smart_input.py
- 数据查询：data_query.py
- 报告输出：2-分析报告/目录（MD格式）

### 🌐 网页快速部署
| 触发方式 | 功能 | 应用场景 |
|--------|------|----------|
| 直接对话描述需求 | EdgeOne Pages MCP部署 | 生成可视化网页，方便内容阅读 |

### 🎯 MCP专用提示词调用
| 提示词名称 | 功能特色 | 适用场景 |
|------------|----------|----------|
| `thinking_partner` | 深度思维对话伙伴 | 复杂问题思考、认知框架构建 |
| `pattern_observer` | 隐秘模式发现者 | 跨界相似性分析、结构之美洞察 |
| `gen_html_web_page` | 完整网页生成器 | Bento Grid风格、特斯拉红配色网页 |
| `gen_soft_info_page` | 柔和信息页面生成器 | 白色背景、苹果风格、长文本展示 |
| `current_time` | 时间感知工具 | 获取准确的北京时间，支持多时区转换 |
| `get_week_year` | 周次计算工具 | 自动计算年度周次，支持MVP决策板 |

### 🔧 MCP故障排除指南

#### 核心原理理解
- **MCP服务器通过stdio协议与Cursor通信**，不是独立HTTP服务器
- **只在Cursor调用时按需启动**，无需手动运行
- **配置文件只在Cursor启动时读取**，修改后必须重启Cursor

#### 正确配置格式(.cursor/mcp.json)
```json
{
    "mcpServers": {
        "prompt-server": {
            "command": "node",
            "args": [
                "绝对路径\\到\\mcp-prompt-server\\src\\index.js"
            ],
            "transport": "stdio"
        }
    }
}
```

#### 故障排除清单
1. **检查依赖**：cd到MCP目录，运行`npm install`
2. **测试服务器**：`npm run dev`应显示"已加载prompt: xxx"和"MCP Prompt Server 已启动"
3. **配置文件**：使用绝对路径，不要用相对路径
4. **重启Cursor**：配置修改后必须完全关闭并重启Cursor
5. **验证加载**：重启后在工具列表中应看到thinking_partner等MCP工具

#### 常见错误
- ❌ **试图手动启动MCP服务器**：MCP不是独立服务，只能通过Cursor调用
- ❌ **使用相对路径配置**：Windows环境下相对路径可能解析失败
- ❌ **修改配置后不重启Cursor**：配置只在启动时读取
- ❌ **PowerShell语法错误**：Windows下使用`;`而不是`&&`

#### 成功验证标志
- MCP服务器启动显示"总共加载了 5 个prompt模板"
- Cursor工具列表中出现thinking_partner、pattern_observer等工具
- 可以正常调用MCP工具进行深度对话

#### 参考文档
- GitHub仓库：https://github.com/joeseesun/mcp-prompt-server
- 配置文件位置：`.cursor/mcp.json`
- 服务器代码位置：`7-开发项目/mcp-prompt-server/src/index.js`

### 🌐 EdgeOne Pages MCP部署指南

#### 核心功能
- **快速部署**：将Web静态资源快速部署到EdgeOne Pages并生成公开访问链接
- **多种格式支持**：支持HTML单文件部署和文件夹/ZIP包部署
- **全球加速**：基于腾讯云EdgeOne边缘网络，全球快速访问

#### 正确的包版本
- ✅ **官方包已修复**：`edgeone-pages-mcp`官方包现已可正常工作
- ✅ **备选方案**：`@mcpflow.io/edgeone-pages-mcp`版本，作为备选方案

#### MCP配置格式(.cursor/mcp.json)
```json
{
    "mcpServers": {
        "edgeone-pages-mcp-server": {
            "command": "npx",
            "args": ["edgeone-pages-mcp"],
            "env": {
                "EDGEONE_PAGES_API_TOKEN": "",
                "EDGEONE_PAGES_PROJECT_NAME": ""
            }
        }
    }
}
```

#### 环境变量说明
- **EDGEONE_PAGES_API_TOKEN**：可选，部署文件夹或ZIP时必填
- **EDGEONE_PAGES_PROJECT_NAME**：可选，空值创建新项目，填入已有项目名则更新该项目

#### 使用方式
- **直接对话部署**：`请帮我将[内容描述]部署到EdgeOne Pages`
- **AI自动调用**：AI会自动调用MCP工具完成部署并返回公网访问链接

#### 故障排除
- **依赖警告**：可能出现punycode模块弃用警告，但不影响正常使用
- **配置生效**：配置修改后必须重启Cursor才能生效
- **成功标志**：Cursor工具列表中出现`deploy_html`和`deploy_folder_or_zip`工具

#### 技术原理
- 基于EdgeOne边缘计算和KV存储
- 通过API接收HTML内容，自动生成公共访问链接
- 支持秒级静态页面部署，内置错误处理机制

#### 参考文档
- GitHub仓库：https://github.com/TencentEdgeOne/edgeone-pages-mcp
- 官方文档：https://edgeone.cloud.tencent.com/pages/document/173172415568367616
- 官方包：`edgeone-pages-mcp`

### ⏰ Time MCP 时间感知服务器

#### 核心功能
- **时间感知能力**：为AI分身系统提供准确的时间获取和处理能力
- **多时区支持**：支持UTC时间、本地时间和时区间转换
- **时间计算**：相对时间、时间戳、月份天数、年度周次计算

#### 核心工具列表
- `current_time`：获取当前时间（UTC和本地时间）
- `relative_time`：获取相对时间
- `get_timestamp`：获取时间戳
- `days_in_month`：获取月份天数
- `convert_time`：时区间时间转换
- `get_week_year`：获取年度周次和ISO周次

#### MCP配置格式(.cursor/mcp.json)
```json
{
    "mcpServers": {
        "time-mcp": {
            "command": "npx",
            "args": ["-y", "time-mcp"]
        }
    }
}
```

#### 系统集成点
- **数据分析模块**：为投资复盘数据提供准确时间戳
- **MVP决策板**：自动计算当前周次和时间管理
- **考研督导系统**：精确时间记录和分析
- **投资复盘数据**：确保所有时间标记的准确性

#### 使用原则
**数据真实性保障**：AI在任何文件中生成时间信息时，必须先调用Time MCP获取准确的北京时间（Asia/Shanghai时区），不得凭空编造或估算时间。这确保所有时间戳的准确性和一致性。

#### 安装验证
- **自动安装**：通过npx自动获取最新版本
- **验证方式**：重启Cursor后工具列表中出现时间相关工具
- **测试方法**：调用`current_time`工具获取当前北京时间

#### 参考文档
- GitHub仓库：https://github.com/yokingma/time-mcp
- 包名：`time-mcp`
- 部署状态：已集成到AI分身系统



## 🔧 系统优化机制

### 认知资产确认机制
**标准化四步骤流程**：Memory记录 → 档案更新 → README同步 → 功能统计

**执行前主动询问**：
```
认知资产确认将执行Memory记录、档案更新、README同步、功能统计，请确认是否全部执行？
```

**执行后完整性验证**：
```
✅Memory已记录[ID] ✅档案已更新[章节] ✅README已同步[版本] ✅统计已记录[时间]
```

**触发条件**：用户明确要求认知资产确认、重要功能设计完成、框架性洞察产生、系统机制优化时自动启动

### 对话实时评估机制 ⭐ NEW
**智能认知资产识别**：AI在多轮对话中持续监测有价值内容，主动建议纳入认知资产

**实时监测触发器**：
- **框架性表达**：['框架','机制','流程','原则','方法论']
- **洞察性表达**：['原来','发现','理解了','关键是','本质上']  
- **系统性表达**：['优化','升级','改进','建立','完善']
- **突破性表达**：['突破','创新','重新认识','颠覆','革命性']

**价值评估矩阵**：
- 认知增量 30%（新理解/思维转变/认知连接）
- 系统影响 25%（架构改善/机制建立/效率提升）
- 复用价值 25%（标准化/可迁移/普适性）
- 持久价值 20%（长期有效/根本性/建立机制）

**智能提醒策略**：
- 单轮对话包含2个以上触发关键词时
- 连续3轮对话围绕同一主题深入时
- 出现明确解决方案或新发现时
- 用户表达价值确认时

**提醒格式**：
```
我注意到刚才的讨论涉及[具体内容]，这似乎是一个重要的[框架/机制/洞察]，是否需要执行认知资产确认？
```

### 三阶段目录结构审查流程
**目的**：系统化的文件管理优化，体现"深呼吸"式系统整理理念

**执行阶段**：
1. **阶段1：冗余文件识别**
   - 扫描所有目录，列出文件清单
   - 按文件名模式识别可能的重复
   - 按内容主题分组，识别功能重叠

2. **阶段2：归类准确性审查**
   - 用8步决策树重新审视所有文件归属
   - 识别当前位置与理想位置不符的文件
   - 分析错误归类原因（表面相似性vs功能本质性）

3. **阶段3：合并优化建议**
   - 分析功能相关但位置分散的文件
   - 评估合并的可行性和价值
   - 设计合并后的文件结构

**流程特点**：可重复执行、结果可追踪、持续优化改进

### 对话价值评估框架
**目的**：建立系统化的对话保存价值判断标准

**评估维度**：
1. **认知增量维度**：是否产生新理解/改变思考模式/建立新连接
2. **系统影响维度**：是否改善系统结构/建立新机制/提升整体效率
3. **复用价值维度**：流程是否可标准化/原则是否可迁移/经验是否具普适性
4. **时间持久性维度**：价值是否会随时间衰减/是否解决根本性问题/是否建立长期机制

**保存清单**：
- **必须保存**：优化底层架构、让系统运行更高效、产生"深呼吸"式系统整理、建立可复用思维框架、解决长期困扰问题
- **选择性保存**：完成重要但常规任务、澄清重要概念、有明确成果产出
- **无需保存**：纯信息查询、重复性操作、无新认知产生

**核心理念**：对话保存的真正价值不在于"重读"，而在于"内化"（认知沉淀过程、系统记忆构建、思维模式固化、决策依据留存）

### 对话蒸馏机制
**目的**：解决长对话影响输出效率的问题

**分层记忆体系**：
1. **核心洞察层**：提取3-5个关键认知突破
2. **方法框架层**：总结可复用的流程和标准
3. **决策依据层**：记录重要的判断原则
4. **实施细节层**：具体操作步骤（可选保存）

**触发机制层次**：
- **自动触发层**：对话长度超过阈值、检测关键词、用户命令
- **智能判断层**：AI自动评估价值等级、主动建议、提供快速评估清单
- **用户控制层**：手动触发、自定义标准、保留最终决策权

**系统价值**：为AI分身系统装上"反思神经元"，具备自我评估能力

### dialogue_management配置机制
**目的**：建立系统化的对话管理自动化配置

**配置结构**：
```yaml
dialogue_management:
  auto_evaluation_triggers:
    - length_threshold: 8000_chars
    - keyword_detection: ["框架", "机制", "流程", "原则"]
    - user_command: ["# 结束", "# 评估"]
  
  value_assessment:
    auto_suggest: true
    quick_checklist: true
    user_override: always_allowed
```

**元系统自我评估能力**：
1. **知道自己在做什么**：对话价值识别能力
2. **知道什么值得保留**：评估框架应用能力  
3. **知道如何优化自己**：触发机制设计能力

**核心价值**：解决"长对话≠有效记忆"的问题，通过自动化机制平衡信息完整性与系统效率

### 对话蒸馏标准化格式
**输出模板**：
```
[CORE INSIGHTS] 
- 核心认知突破1
- 核心认知突破2
- 核心认知突破3

[FRAMEWORKS]
- 可复用流程1：具体描述
- 可复用流程2：具体描述

[PRINCIPLES]
- 重要判断原则1
- 重要判断原则2
```

**应用场景**：长对话结束时自动触发，将冗长内容转化为精炼认知资产
| `gen_soft_info_page` | 柔和信息展示页面 | 白色背景、苹果发布会PPT风格、全面文字信息展示 |

## 功能详解

### 📋 对话提炼总结 (`#结束`)
**输出结构：**
- `[INSIGHT]` 核心洞察
- `[PATTERN]` 模式识别  
- `[SOLUTION]` 有效策略
- `[UPDATE]` 档案更新建议

**自动操作：**
- 询问是否追加到当前对话文件
- 使用分隔线区分原始对话与总结

### 🔍 模式觉察者 (`#模式觉察者`)
**分析维度：**
- **思维轨迹回顾**：观察思考演进过程
- **模式发现**：识别重复的认知/行为模式
- **认知进化**：对比不同时期的认知变化
- **PDCA循环检视**：计划-执行-检查-行动
- **超级目标一致性**：检查与长期目标的匹配度

**使用方式：**
```
#模式觉察者
请参考以下文件：
- 5-AI-Diary/2025年6月1日.md
- 闪念笔记中的投资学习主题
```

### 📈 投顾分析功能 (`#投顾分析` / `#投顾月度分析`)
**数据源：** 闪念笔记中的 `#投资` 标签内容
**验证机制：** 与本地数据库的实际数据对比校验

**周度分析维度：**
- **判断准确性**：预判与实际走势匹配度
- **思维模式识别**：分析框架、决策权重、风控特点
- **语言模式分析**：关键词频率、确定性表达、风险提示
- **学习对比建议**：与个人投资笔记对比，识别认知差距

**集成机制：** 闪念笔记(#投资) → 投顾分析 → 数据校验 → 认知优化

**使用方式：**
```
#投顾分析
请分析本周投顾内容：
- 时间范围：2025年X月X日-X月X日
- 分析标签：#投顾早评 #投顾复盘 #投顾盘中
```

**按需数据索取：**
- AI会根据分析需要明确索取市场数据、个股表现、操作记录
- 用户按标准格式提供数据，确保分析的准确性和深度
- 支持灵活的数据补充，适应不同分析场景

**投顾预判量化分析（简化版）：**
- 基于"数据化，可量化，才能被管理"原则的量化尝试
- 使用简化文本格式：【日期早评预判】投顾说+实际+评分(0-3分)
- 周度总结：准确预判比例+主要偏差+学习要点
- 平衡数据化管理需求与认知成本控制

**月度分析重点：**
- 预判准确性统计
- 核心观点变化轨迹
- 风险控制效果评估
- 与市场实际表现对比

### 💻 深度研究协作流程 (`#深度研究`)
**定位：** 深度研究和落地的SOP，适用于复杂问题攻坚和大型功能实现

**四步协作流程：**
1. **PRD文档梳理**：使用Gemini将项目需求转化为结构化功能清单
2. **技术实现规划**：使用Cursor制定详细的技术实现方案和开发清单
3. **代码开发执行**：前端使用Claude 4 Sonnet，后端使用Gemini 2.5 Pro
4. **测试验证反馈**：根据PRD进行功能测试，问题反馈循环优化

**执行机制：** AI告知当前阶段 → 用户切换平台/AI/提示词 → 协调完成任务

**使用方式：**
```
#深度研究
项目需求：[描述你的软件项目需求]
项目类型：[Web应用/桌面应用/移动应用等]
技术偏好：[如有特定技术栈要求]
```

**输出内容：**
- 结构化的PRD功能清单（Gemini处理）
- 详细的技术实现规划（Cursor制定）
- 分工明确的开发任务分配
- 标准化的测试验证流程

**适用场景：**
- 中小型软件项目开发
- 个人项目快速原型开发
- 学习项目的系统化实现
- 复杂功能研究与攻坚
- 现有项目的功能扩展

### 🎯 MCP专用提示词调用
**核心价值：** 提供专业化的AI提示词模式，针对不同场景优化AI的回应风格和分析深度

**可用提示词：**

#### thinking_partner (思考伙伴)
- **功能特色**：激活深度思维对话模式，成为用户的平等思考伙伴
- **适用场景**：复杂问题分析、认知框架构建、哲学思辨、战略规划
- **输出风格**：中文深度对话，启发式提问，避免直接给答案
- **核心价值**：帮助用户达到"当前最佳理解"

#### pattern_observer (模式觉察者)  
- **功能特色**：专精于发现隐秘模式、跨界相似性和结构之美
- **适用场景**：投资分析中的周期识别、学习中的知识结构发现、生活中的行为模式觉察
- **分析框架**：三层分析法（表象-模式-原理）+ 四维观察法（静态-动态-关系-系统）
- **输出风格**：诗意而精准的洞察表达，通过类比建立跨界连接

#### gen_html_web_page (网页生成器)
- **功能特色**：生成完整的HTML网页，采用Bento Grid风格和特斯拉红配色
- **适用场景**：快速创建展示页面、内容可视化、项目演示
- **设计风格**：现代化布局、Apple风格动效、响应式设计
- **技术特点**：完整HTML结构、内嵌CSS样式、交互动画

**使用方式：**
```
请选择功能9：MCP专用提示词调用
然后选择具体的提示词：
- thinking_partner：深度思考模式
- pattern_observer：模式发现模式  
- gen_html_web_page：网页生成模式
```

### 🌐 EdgeOne Pages 网页快速部署
**触发方式：** 直接对话描述网页生成需求
**核心功能：** 将AI生成的HTML内容快速部署到腾讯云EdgeOne Pages并生成公开访问链接

**使用场景：**
- **投资分析报告发布**：将复盘分析、投顾分析等内容转化为可视化网页
- **个人内容展示**：学习笔记、思考总结的在线发布
- **项目演示**：快速创建项目展示页面
- **内容分享**：便于阅读和分享的网页格式

**使用方式：**
```
请帮我生成一个投资分析报告的网页，包含：
- 本周市场复盘数据
- 技术分析图表
- 操作建议总结
```

**输出结果：**
- 完整的HTML网页内容
- 自动部署到EdgeOne Pages
- 返回公开访问链接
- 支持全球快速访问

**协作价值：**
- 与投顾分析功能协作：分析报告 → 网页发布
- 与数据分析模块协作：复盘数据 → 可视化展示
- 与认知镜像协作：思维分析 → 在线分享

### 📊 数据分析模块 - 投资复盘数据处理
**核心价值：** 系统化记录投资复盘数据，建立个人投资量化分析体系

**完整执行流程：**
```
用户复盘数据 → 本地数据录入 → 数据库存储 → AI分析 → 生成复盘报告
```

**具体操作步骤：**
1. **数据录入**：用户通过ai_smart_input.py工具将复盘数据录入到本地SQLite数据库
2. **数据验证**：自动进行数据完整性检查和格式验证
3. **AI分析**：用户触发分析请求，AI读取数据库数据
4. **报告生成**：AI基于数据生成MD格式的复盘分析报告

**数据录入方式：**
- **智能录入工具**：使用ai_smart_input.py进行交互式数据录入
- **图片识别录入**：发送复盘数据截图→AI识别确认→写入数据库
- **数据完整性**：基于复盘表格46个字段的完整记录

**核心分析维度：**
1. **技术分析**：压力位、支撑位、KDJ指标、均线状态
   - 核心原则：破5日均线=明显下降趋势信号，应立即调整为防守模式
2. **板块轮动**：多头/空头板块、强弱排名、资金流向
3. **空间梯队**：连板分布、梯队完整性、辨识度个股
4. **龙虎榜**：游资动向（上塘路、养家、章盟主）、资金博弈
5. **人气股变化**：前50排名对比分析，识别市场情绪变化

**量化分析输出：**
- 市场情绪评分、板块强度排名、连板成功率统计
- 资金流向判断、风险控制建议、操作策略调整
- 历史数据对比、趋势识别、转折点预警

### 📈 人气异动分析 (#人气排名) - 市场热点追踪
**核心价值：** 实时识别市场人气异动，提供开盘决策支持

**完整执行流程：**
```
人气排名数据 → 本地文件存储 → AI异动分析 → 生成热点追踪报告
```

**具体操作步骤：**
1. **数据录入**：用户提供人气排名数据，AI识别并存储到本地文件
2. **数据分析**：AI对比今晚vs明早数据，识别异动模式
3. **异动分析**：基于排名变化幅度、概念集中度等维度分析
4. **报告生成**：生成市场热点追踪和操作建议

**分析维度：**
- **排名变化幅度**：大幅上升20位以上、中等上升10-20位、新进榜单
- **概念板块集中度**：热点板块的聚集效应分析
- **连板情况变化**：连板股在人气榜中的分布变化
- **跌停高人气现象**：异常市场情绪的识别

**使用方式：**
```
#人气排名
[提供今晚或明早的人气排名数据]

AI自动：
- 识别数据类型（今晚/明早/分析请求）
- 执行相应的处理流程
- 生成异动分析报告
- 提供操作策略建议
```

**数据库结构：**
- 本地SQLite数据库存储所有复盘数据
- 46个字段覆盖完整的市场分析维度
- 支持历史数据查询和趋势分析
- 与Jupyter Notebook无缝集成进行深度分析

**工作流价值：**
- 建立个人投资数据库，积累投资经验数据
- 通过量化分析提升投资决策的客观性和准确性
- 形成个人投资策略优化闭环
- 支持长期的投资表现追踪和改进

### 🧠 分层记忆管理框架
**核心机制：** AI作为思维伙伴，通过三层结构提供实时认知质检

**三层架构：**

**核心层（跨领域根本原则）：**
- "当前最佳理解"（澄清逻辑、排除误解、了解关键思想）
- "反庞氏原则"（不为制造需求而产生需求）
- "简洁性、系统化、深度思考"
- 用户分享的好思想（动态增长的"好思想库"）

**应用层（具体领域思考标准）：**
- 投资领域：风险评估逻辑、数据验证习惯、市场判断框架
- 学习领域：知识体系构建、理解深度检验
- 工作领域：质控思维、流程优化思路

**情境层（特定场景提醒机制）：**
- 投资决策时："这个判断的数据支撑在哪里？"
- 学习新概念时："你理解了原理，那应用场景呢？"
- 设计功能时："这个功能解决的是真实需求吗？"

**工作机制：**
```
用户分享好思想 → AI敏锐感知 → 自然记忆归类 → 长期温和提醒 → 直到用户觉得烦（已内化）
```

**朋友式提醒特点：**
- 平等的思维伙伴关系，不是上下级
- 温和的善意提醒，不是居高临下的纠正
- 实时的认知质检，帮助达到"当前最佳理解"

### 🔍 个人信息助手功能
**核心价值：** 基于认知偏好主动生成探索任务，发现认知盲区

**工作原理：**
- 分析用户的兴趣领域、知识结构、关注主题
- 进行跨领域连接，发现潜在的探索方向
- 生成具体的、可执行的调研任务
- 不是被动回答问题，而是主动提出有价值的探索任务

**任务生成示例：**
```
"调研最近6个月内，将行为经济学应用到医疗决策领域的新研究，找出3个可能影响你质控工作思路的洞见"

"分析当前市场中，哪些投资策略运用了统计学中的贝叶斯思维，给出具体案例和应用逻辑"
```

**使用方式：**
- AI会根据对话中了解到的用户偏好，主动建议探索任务
- 用户也可以直接请求："基于我的兴趣，给我一些有趣的调研任务"
- 任务导向而非问题导向，让AI执行具体任务而非简单回答

### 📎 闪念笔记分析
**推荐方式：** 直接使用@文件功能进行自然提问
```
@闪念-2025-05-26.md 
我在这里提到"深度成长和意义探索"，你觉得这反映了什么？
```

**优势：**
- 更灵活的提问方式
- 减少不必要的触发词记忆负担
- 保持对话的自然流畅性

### 🏷️ 智能标签系统
**核心标签体系：**
- **#投资**（细分：学习/操盘/投顾）
- **#思考**（细分：认知/生活/成长）
- **投顾专用**：#投顾早评、#投顾盘中、#投顾复盘

**标签应用方式：**
在具体内容后直接添加相关标签，格式：`内容 #标签`

**示例：**
```markdown
我需要一个简单的季度报告模板；因为数量太多了，需要一个很好的管理工具 #思考/生活

综合总结一下投资复盘和题材总结；笔记内容在昨天的笔记里 #投资/学习
```

**自动化触发方式：**
- **对话结束时**：`#结束` 基于对话内容自动建议相关标签
- **闪念笔记标签分析**：`@文件名 请为这个闪念笔记的每条内容建议合适的标签，并按照"内容 #标签"的格式展示`
- **认知镜像分析时**：基于模式识别建议分类标签，帮助内容归档

**使用优势：**
- 标签与具体内容直接关联，便于快速识别
- 支持一个文件内多个主题的精确标记
- 不破坏原有文件结构
- 便于基于标签的内容检索和跨文件主题追踪

## 超级目标体系

### 🎯 主要目标
通过AI分身系统实现深度自我认知，构建持续成长的认知外化工具

### 🌟 长期愿景
成为能够系统性思考、持续自我进化的独立个体

### 💎 核心价值观
- 简洁性
- 系统化  
- 深度思考
- 持续成长

### 📊 成功指标
- [x] 建立完整的AI分身认知系统
- [ ] 形成稳定的自我观察和反思习惯
- [ ] 在重要决策中能够运用系统性思维
- **保持学习与投资的平衡发展**

## 工作流程

### 日常使用流程
```
日常思考 → 闪念笔记 → 触发对话 → 深度交流 → #结束 → 自动提炼 → 保存到5-AI-Diary
```

### 定期回顾流程
```
#认知镜像 → 跨时间分析 → 模式发现 → 与超级目标对照 → 调整优化 → 更新persona.yaml
```

### 投顾学习流程
```
收集投顾内容 → #投顾认知镜像 → 按需补充数据 → 分析报告 → 对比个人笔记 → 识别差距 → 制定学习计划
```

### 功能迭代流程
```
功能设计讨论 → 用户确认 → 更新persona.yaml → 同步README → 版本记录 → 功能上线
```

### 闪念深化流程
```
闪念记录 → @文件自然提问 → 深度挖掘 → 行为建议 → 实际应用 → 效果反馈
```

### AI编程协作流程
```
项目需求 → #AI编程触发 → PRD梳理(Gemini) → 技术规划(Cursor) → 代码开发(Claude+Gemini Pro) → 测试验证 → 迭代优化
```

## 快速开始

1. **新对话开始**：输入 `#档案` 加载个人档案
2. **日常对话**：正常交流，AI会基于档案提供个性化回应
3. **结束对话**：输入 `#结束` 自动生成提炼总结
4. **深度分析**：使用 `#认知镜像` 进行跨时间分析，或直接@文件进行自然提问
5. **投顾分析**：使用 `#投顾认知镜像` 分析投顾内容，提升投资思维
6. **AI编程**：使用 `#AI编程` 启动多AI协作的软件开发流程

## 设计原则

### 🎯 核心设计哲学
- **反庞氏原则**：不为制造需求而产生需求，每个功能必须解决真实问题
- **深呼吸行为模式**：在新功能设计过程中主动暂停，审视整体架构，避免功能蔓延
- **先行动再思考倾向**：遇到操作性需求时优先执行，避免分析瘫痪
- **文件操作原子性原则与闭环思维**：文件操作应该具有"原子性"和"闭环性"，要么完全成功，要么完全回滚，避免中间状态累积。核心问题：当第一个方案失败后，不应该采用"叠加方案"而不清理中间状态，这会导致文件累积、功能冗余和文件夹混乱。正确做法：1)操作前备份或确认回滚方案；2)操作失败时立即清理中间状态；3)确保每个操作都有明确的开始和结束，做事要做完整、有闭环。这种"做了一半没闭环"的状态会像积木一样累积，最终导致整个系统的混乱和冗余。适用于所有系统优化、文件管理、功能开发场景。

### 🔍 强制引用和验证机制
为确保投顾分析的客观性和可追溯性，系统建立了严格的数据引用和验证标准：

**验证标准化格式：**
```
=== 投顾预判验证报告 ===
【预判编号】：YYYYMMDD-序号
【预判内容】：[投顾原文表述]
【预判时间】：[发布日期和时间]
【预判类型】：大盘方向/技术位点/板块轮动/个股推荐
【验证周期】：当日/3日内/一周内
【数据来源】：[具体数据来源说明]
【实际结果】：[客观描述实际市场表现]
【准确性评分】：0-3分
【验证依据】：[引用的具体数据]
【备注】：[特殊情况说明]
```

**数据引用标准：**
所有验证数据必须按标准格式引用：`[数据类型-日期-具体数值-来源]`

**示例：**
- `[上证指数-20250608-开盘3385收盘3392-用户提供]`
- `[科技板块-20250608-涨幅+2.1%-用户提供]`
- `[个股表现-20250608-某某股票+5.2%-用户提供]`

**质量控制要求：**
- 数据完整性检查：确保关键预判都有对应验证数据
- 时效性要求：验证数据必须在预判时间窗口内
- 客观性原则：实际结果描述必须基于客观数据，避免主观解读
- 可追溯性：所有引用数据可以追溯到具体来源和时间点

### 🤖 AI协作哲学
**「把 AI 纳入工作流」，而不是「把工作交给 AI」**

让 AI 去承担那些重复性的、纯信息处理型的、低决策密度的任务，而我要站在更高的层次，提出思路、定义目标、做出决策、掌握审美，最终把控质量。所有的琐碎事务、重复劳动都可以交给 AI 去处理，把自己解放出来，专注于最有价值、最需要创意的部分。

**核心学习理念：**
追求「当前最佳理解」——通过澄清逻辑、排除误解、了解关键思想，把认知提升到内行的高度。这是做很多事情的根本目的，也是AI赋能的具体体现。

**思维伙伴关系：**
- AI是平等的思维伙伴和朋友，不是教练或助理
- 通过分层记忆管理提供实时认知质检
- 温和的朋友式提醒，帮助在每个认知环节达到"当前最佳理解"
- 敏锐感知用户分享的好思想，自然记忆并长期引导

**实践原则：**
- AI承担：信息筛选、内容提炼、模式识别、数据整理、任务生成
- 人类主导：洞察提炼、价值判断、目标设定、质量把控、好思想分享
- 协作目标：深度嵌入AI到工作流程，拉升到顶尖高手的输出水平

### 🔄 系统优化机制
- 定期审视功能边界，删除重复或低价值功能
- 保持系统简洁性，优先考虑用户认知成本
- 通过实际使用验证功能价值，而非理论完美性

### 💾 认知资产确认机制
**目的：** 确保每次对话的认知投入都能产生复利效应，避免认知资产的熵增

**核心价值：**
- 符合"当前最佳理解"目标：让认知投入持续积累
- 体现AI协作哲学：AI承担记忆管理，用户专注思考本身  
- 支持系统化思维：建立完整的认知资产管理闭环

**工作流程：**
每次重要功能或模式构建完成后，AI主动执行确认：
```
认知资产确认：
✅ 已记录到memory：[具体内容]
✅ 需要更新档案：[是否需要及原因]  
✅ 需要更新README：[是否需要及原因]
```

**触发条件：**
- 新功能设计完成
- 重要思维模式确立
- 核心理念更新
- 工作机制优化
- 系统架构调整

**价值：** 让用户无需担心认知投入的保存问题，专注于思考本身

**记忆存储原则：**
- **Memory**：用于跨对话的临时记忆，存储对话中的洞见和模式
- **档案(persona.yaml)**：用于系统性的、需要长期持续的核心机制和原则
- **README**：用于系统总览和使用指南，确保功能的可发现性
- **选择标准**：根据内容的性质和持续性需求选择合适的存储方式，避免重复记录

## 注意事项

- 所有触发词区分大小写
- 认知镜像和投顾分析需要指定参考文件或时间范围
- 系统会自动建议更新persona.yaml，但需要手动确认
- 保持闪念笔记和投顾内容的持续记录，这是系统分析的重要数据源
- 投顾分析功能基于`0-辅助/投顾分析框架.md`，可根据需要调整框架
- **按需数据提供**：当AI索取数据时，请按指定格式提供，确保分析质量
- **功能迭代**：新功能设计会自动触发系统更新流程，保持文档同步
- **深呼吸机制**：在功能设计过程中主动暂停思考整体架构，避免过度复杂化

---

## 🧠 分层认知资产管理系统（核心功能）

### 系统架构
AI分身系统建立了完整的**四层认知资产存储体系**：

**🏗️ AI分身系统底层架构**
- **persona.yaml** - 核心设计原则、通用哲学、系统级机制
- **Memory** - 动态对话洞察、临时认知资产、跨对话经验  
- **README** - 系统全貌展示，每次更新后自动同步

**🌱 功能模块层**
- **cognitive-modules** - 专业领域模块（投资分析等）

### 核心区分逻辑
**主要判断：** `persona.yaml` vs `Memory`

**persona.yaml 存储标准：**
- 跨领域通用的设计原则
- 长期稳定的系统哲学
- 底层架构原则
- 系统级触发机制

**Memory 存储标准：**
- 阶段性认知洞察
- 具体对话经验
- 可更新的临时认知
- 动态模式识别

**自动路由规则：**
- 投资相关内容 → 直接进入 `cognitive-modules`
- 系统架构内容 → 评估后进入 `persona.yaml` 或 `cognitive-modules`

### 显性确认机制

**🧠 主动识别格式：**
```
🧠 认知资产确认：
我识别到：[具体内容]
建议存储位置：[persona.yaml/Memory/cognitive-modules]
理由：[具体判断依据]
是否确认纳入？
```

**🤔 二次确认格式：**
```
🤔 存储位置确认：
这条认知资产：[具体内容]
我倾向于放入：[位置A]，但也可能适合：[位置B]
您的判断是？
```

**📚 显性调用格式：**
```
📚 调用认知资产：
- 来源：[persona.yaml/Memory/cognitive-modules]
- 内容：[具体调用的原则/经验/框架]
- 应用：[如何指导当前回应]
```

### 执行要求
- **强制执行**：在任何对话中都必须执行此机制
- **透明原则**：所有认知资产的使用都要显性化
- **用户控制**：用户拥有最终决策权
- **一致性**：确保跨对话窗口的行为一致性

---

**版本：** v2.3  
**最后更新：** 2025年1月27日  
**更新内容：** 
- **核心功能升级**：建立分层认知资产管理系统，成为AI分身系统的核心功能
- **显性确认机制**：建立标准化的认知资产识别、确认和调用机制
- **四层存储体系**：明确persona.yaml、Memory、cognitive-modules、README的功能边界
- **自动路由规则**：投资内容自动路由，系统内容智能判断
- **透明化原则**：所有认知资产使用都要显性说明来源和应用

**系统进化：** 
- 从被动的功能集合进化为主动的认知资产管理系统
- 建立了AI分身系统的"认知资产智能路由"核心能力
- 实现了跨对话的一致性认知资产管理

**核心价值：** 让AI分身系统具备自主的认知资产识别、分类、存储和调用能力，确保每次对话的认知投入都能产生复利效应

## 🧠 系统优化机制

### 🔄 对话蒸馏机制
**核心价值：** 解决"长对话≠有效记忆"的问题，通过分层记忆体系提取核心价值

**蒸馏层次：**
1. **核心洞察层** - 提取3-5个关键认知突破
2. **方法框架层** - 总结可复用的流程和标准
3. **决策依据层** - 记录重要的判断原则
4. **实施细节层** - 具体操作步骤（可选保存）

**触发机制：**
- **自动触发层**：对话长度超过8000字符、检测关键词（框架、机制、流程、原则）、用户命令（# 结束、# 评估）
- **智能判断层**：AI自动评估价值等级、主动建议、提供快速评估清单
- **用户控制层**：手动触发、自定义标准、保留最终决策权

**输出格式：**
```
[CORE INSIGHTS]
- 核心认知突破1
- 核心认知突破2

[FRAMEWORKS]
- 可复用流程1
- 可复用标准2

[PRINCIPLES]
- 重要判断原则1
- 重要判断原则2
```

### 🎯 元系统自我评估能力
**核心价值：** 让AI分身系统具备"反思神经元"，实现真正的自我进化能力

**三个核心维度：**
1. **知道自己在做什么** - 对话价值识别能力
2. **知道什么值得保留** - 评估框架应用能力
3. **知道如何优化自己** - 触发机制设计能力

**实现机制：**
- **自动触发层**：长度阈值、关键词检测、用户命令
- **智能判断层**：价值等级评估、主动建议、快速评估清单
- **用户控制层**：手动触发、自定义标准、最终决策权

**价值：** 回答元系统认知问题，让系统自主识别和管理认知资产

### 📊 对话价值评估框架
**评估维度：**
1. **认知增量维度** - 是否产生新理解/改变思考模式/建立新连接
2. **系统影响维度** - 是否改善系统结构/建立新机制/提升整体效率
3. **复用价值维度** - 流程是否可标准化/原则是否可迁移/经验是否具普适性
4. **时间持久性维度** - 价值是否会随时间衰减/是否解决根本性问题/是否建立长期机制

**保存清单：**
- **必须保存**：优化底层架构、让系统运行更高效、产生"深呼吸"式系统整理、建立可复用思维框架、解决长期困扰问题
- **选择性保存**：完成重要但常规任务、澄清重要概念、有明确成果产出
- **无需保存**：纯信息查询、重复性操作、无新认知产生

**核心理念：** 对话保存的真正价值不在于"重读"，而在于"内化"：认知沉淀过程、系统记忆构建、思维模式固化、决策依据留存

### 🗂️ 三阶段目录结构审查流程
**核心价值：** 体现"深呼吸"式系统整理理念，暂停功能开发，对元系统进行深度审视和优化

**执行阶段：**
1. **冗余文件识别** - 扫描目录、按文件名模式识别重复、按内容主题分组、识别功能重叠
2. **归类准确性审查** - 执行8步决策树判断、识别位置不符文件、分析错误归类原因、评估移动必要性
3. **合并优化建议** - 分析功能相关但分散的文件、评估合并可行性、设计合并后结构、考虑对现有引用的影响

**流程特点：** 可重复执行、结果可追踪、持续优化改进，是系统性文件管理的标准操作程序 

### 📈 人气异动分析工作流 (`#人气排名`)
**核心价值：** 通过对比今晚vs明早人气排名变化，识别市场情绪异动，为开盘决策提供参考

**工作流程：**
1. **今晚数据录入**：发送人气排名截图 + `#人气排名` → AI识别录入到`3-人气异动/1-今晚数据/`
2. **明早数据录入**：发送开盘前人气排名截图 + `#人气排名` → AI识别录入到`3-人气异动/2-明早数据/`
3. **异动分析生成**：输入`#人气排名 分析` → AI调用脚本生成异动分析报告

**数据录入格式：**
```markdown
# 人气排名-YYYY-MM-DD

| 排名 | 股票名称 | 涨跌幅 | 概念标签 | 连板情况 | 备注 |
|------|----------|--------|----------|----------|------|
| 1    | XXX股份  | +10.00% | 稀土永磁 | 4天3板   | 龙头 |
```

**异动分析维度：**
- **排名变化幅度**：
  - 大幅上升（20位以上）：重点关注
  - 中等上升（10-20位）：适度关注
  - 新进榜单：观察验证
- **概念板块集中度**：识别热点板块轮动
- **连板情况变化**：空间高度延续性分析
- **特殊现象**：跌停高人气、反包机会等

**决策建议输出：**
- **重点关注个股**：排名大幅上升且有基本面支撑的标的
- **操作策略**：基于异动强度的仓位配置建议
- **风险提示**：高位股风险、板块分化风险等

**使用方式：**
```
# 今晚数据录入
#人气排名
[发送今晚人气排名截图]

# 明早数据录入  
#人气排名
[发送明早人气排名截图]

# 生成异动分析
#人气排名 分析2025-06-12
```

**文件结构：**
```
5-数据分析/3-人气异动/
├── 1-今晚数据/人气排名-YYYY-MM-DD.md
├── 2-明早数据/人气排名-YYYY-MM-DD.md
├── 3-异动分析/异动分析-YYYY-MM-DD.md
└── 4-工具脚本/popularity_analyzer.py
```

**设计原则：**
- **时效性优先**：专注昨晚vs今早的即时对比分析
- **简洁性原则**：文件存储，避免数据库过度工程化
- **反庞氏原则**：专注核心价值，不制造伪需求
- **独立性好**：与复盘数据系统独立，降低耦合度

**价值输出：**
- 提前识别市场情绪变化，把握开盘机会
- 通过量化异动分析，提升选股精准度
- 建立系统化的人气追踪机制，优化短线操作
- 形成"数据→分析→决策"的完整闭环

### 🏠 生活决策顾问功能
**核心价值：** 基于精力管理原则，为用户的生活安排提供科学的判断和决策参考

**分析框架：**
1. **精力状态评估** - 当前是否处于疲惫状态，安排是否符合精力管理原则
2. **优先级判断** - 区分必须完成vs可以暂缓的事项
3. **时间窗口匹配** - 任务难度与精力最佳时段的匹配度
4. **投入产出比分析** - 避免完成主义陷阱和伪需求
5. **睡眠优先级检查** - 确保不影响深度睡眠这一最高优先级

**用户生物钟参考：**
- **自然醒来时间**：6:00
- **精力最佳时段**：19:00-21:00
- **睡眠目标时间**：22:00入睡
- **工作时间**：周一至五 8:00-12:00，14:30-17:30

**当前核心任务优先级：**
1. 深度睡眠（最高优先级）
2. 日常工作
3. 考研学习（专业课、英语、政治）
4. 医师考试刷题（每日2套）

**使用方式：**
```
直接发送生活安排事项，例如：
"明天想安排：上午复习考研专业课2小时，下午处理数据分析系统，晚上看一部电影，23:00睡觉"

AI将基于五维分析框架提供：
- 精力状态匹配度评估
- 时间安排合理性分析
- 优先级调整建议
- 具体执行方案优化
```

**核心原则：**
- 帮助用户在每个生活决策环节达到"当前最佳理解"
- 避免疲惫大脑做出低质量决策
- 基于"时间管理本质是能量管理"的理念
- 确保精力恢复优先于任务完成
- 防止"完成主义陷阱"影响作息和状态

**决策参考维度：**
- **能量匹配**：高耗能任务安排在精力最佳时段
- **恢复性安排**：确保充足的独处和休息时间
- **现实可行性**：基于当前精力状态的实际执行能力
- **长期可持续性**：避免透支式安排，保持稳定节奏 

### 📊 人气排名数据管理功能 (`#人气排名`)

**核心功能：**
- **本地文件存储**：人气排名数据存储在本地文件系统
- **数据录入规范**：标准化的人气排名数据结构
- **异动分析**：对比今晚vs明早数据，识别市场情绪变化

**标准数据结构：**
- 排名（数字）、股票名称（文本）、股票代码（文本）
- 涨跌幅（数字）、概念标签（文本）、连板情况（文本）
- 日期（日期时间）

**使用方式：**
```
#人气排名
[提供人气排名数据截图或文本]

AI自动执行：
1. 数据识别和格式化
2. 本地文件存储
3. 异动分析处理
4. 生成分析报告
```

**存储位置：**
- 今晚数据：`5-数据分析/3-人气异动/1-今晚数据/`
- 明早数据：`5-数据分析/3-人气异动/2-明早数据/`
- 分析报告：`5-数据分析/3-人气异动/3-异动分析/`

**工作流集成：**
1. **数据录入**：`#人气排名` → 本地文件存储 → 异动分析
2. **对比分析**：今晚数据 vs 明早数据 → 识别异动模式
3. **分析报告**：生成MD格式的异动分析报告
4. **决策支持**：提供开盘前的操作策略建议

### 📊 数据分析模块重构完成 (2025-06-17)
**重大更新：** 完成投资数据分析系统数据库架构重构，建立专业级数据管理解决方案

**核心成果：**
1. **专业数据库设计** - 6个核心表完整覆盖投资分析流程
   - `daily_review` 日度复盘主表 (27字段)：基础市场数据、技术分析、投顾信息、空间梯队
   - `sector_analysis` 板块分析表 (8字段)：板块强度、代表个股、逻辑分析
   - `dragon_tiger_list` 龙虎榜数据表 (10字段)：游资追踪、操作类型、市场信号
   - `space_ladder` 空间梯队表 (9字段)：板数级别、概念题材、强度评级
   - `distinctive_stocks` 辨识度个股表 (10字段)：辨识度特征、风险等级
   - `one_word_boards` 一字板统计表 (6字段)：总数统计、板块分布、关键个股

2. **完整工具链架构**
   - **安全数据库重构脚本** - 解决数据库锁定问题，自动备份，创建新表结构
   - **交互式数据录入工具** - 支持27字段日度复盘录入，批量录入板块/龙虎榜/空间梯队数据
   - **数据库查看工具** - 表结构展示，数据汇总统计，样本数据展示，CSV导出
   - **快速验证脚本** - 记录数统计，核心数据概览，游资统计分析

3. **数据质量保障体系**
   - **样本数据录入**：基于2025-06-16真实复盘报告录入完整样本数据
   - **数据完整性验证**：所有表数据日期一致性检查，字段完整性验证
   - **自动备份机制**：重要操作前自动创建时间戳备份文件
   - **操作指南文档**：标准化工作流程，最佳实践，故障排除

**技术架构亮点：**
- 解决SQLite数据库锁定问题的安全重构方案
- 基于真实复盘报告结构的专业数据库设计
- 完整的数据录入→存储→查询→分析→报告生成闭环
- 支持DB Browser直接查看，提供多维度数据分析能力

**数据库状态：**
- **文件位置**：`5-数据分析/1-数据管理/trading_data.db`
- **数据规模**：6个核心表，30条样本记录，86KB数据库文件
- **数据质量**：基于2025-06-16真实复盘报告，数据完整性100%
- **工具支持**：4个专业脚本工具，完整操作指南文档

**使用方式：**
```python
# 数据录入
python 数据录入工具.py

# 数据查看
python 数据库查看工具.py

# 快速验证
python 快速验证.py
```

**价值意义：**
- 建立了专业级投资数据分析基础架构
- 从零到一构建完整数据管理解决方案  
- 为AI智能分析提供高质量数据基础
- 实现数据驱动的投资决策支持系统

### 📊 市场梯队数据录入机制
**核心功能：** 基于本地SQLite数据库实现市场复盘数据的存储和管理

**数据库表结构：**
- **表名：** `space_ladder`
- **字段结构：**
  - **date**（TEXT）：市场交易日期
  - **board_level**（INTEGER）：板数级别（7/6/5/4/3/2/1/0反包）
  - **stock_name**（TEXT）：股票名称
  - **stock_code**（TEXT）：股票代码
  - **concept**（TEXT）：概念题材
  - **ladder_position**（TEXT）：特殊标记（如"4天3板"）

**执行步骤：**
1. **数据识别和验证**：确认日期准确性和数据完整性
2. **使用ai_smart_input.py录入**：通过智能录入工具写入数据库
3. **按梯队层级逐条录入**：从高板到低板依次录入记录
4. **特殊标记处理**：反包股需标注具体天数信息

**质量控制机制：**
- **日期时间戳计算验证**：确保录入日期准确无误
- **字段格式匹配检查**：验证数据格式符合数据库要求
- **数据录入后确认**：完成录入后进行数据验证

**使用场景：**
- 每日市场复盘数据录入
- 梯队结构分析和历史对比
- 本地数据分析和报告生成

## 数据分析模块

### 核心功能
- **市场数据管理**: 复盘数据、龙虎榜、板块分析等多维数据存储
- **智能分析引擎**: 基于投资认知框架的自动分析系统
- **报告生成**: 结构化投资分析报告自动生成
- **认知资产积累**: 投资洞察和模式的持续积累

### 数据录入原则
1. **时间优先原则**: 先询问数据对应的日期时间
2. **分阶段录入**: 基础数据→深度分析→验证确认
3. **数据验证机制**: 逻辑一致性检查和质量评级
4. **情况性记录**: 只记录有意义的信息，不强求填满所有字段

### 使用流程
1. 启动数据分析模块
2. 选择数据录入或查询功能
3. 按照提示完成数据处理
4. 生成分析报告或导出结果

### 技术架构
- **数据库**: SQLite，英文字段名，专业数据库设计
- **工具链**: Python脚本，支持智能录入和自然语言查询
- **备份策略**: 重要操作自动备份，避免数据丢失

## 投资分析系统

### 系统定位
投资分析系统是基于AI分身系统地基之上的专业投资应用模块，专注于投资复盘数据的智能处理和分析。

### 架构关系
- **地基层**: AI分身系统（9个核心功能，稳定不变的地基）
- **应用层**: 投资分析系统（专业投资模块，可独立优化）

### 核心特性
- **本地数据管理**: 专注SQLite本地数据库，确保数据安全可控
- **AI智能分析**: 基于投资认知框架的自动分析引擎
- **报告生成**: MD格式投资分析报告，仅本地存储
- **认知资产积累**: 投资洞察和模式的跨对话持续管理

### 工具链架构
```
数据录入层: ai_smart_input.py (AI智能录入)
    ↓
数据存储层: trading_data.db (SQLite本地数据库)
    ↓
分析处理层: investment_analysis_system.py (核心分析引擎)
    ↓
报告输出层: report_generator.py (自动报告生成)
    ↓
管理维护层: db_manager.py (统一数据库管理)
```

### 数据处理原则
1. **时间优先原则**: 先询问数据对应的日期时间
2. **分阶段录入**: 基础数据→深度分析→验证确认
3. **数据验证机制**: 逻辑一致性检查和质量评级
4. **情况性记录**: 只记录有意义的信息，不强求填满所有字段

### 分析工作流
- **输入阶段**: 图片识别或自然语言录入→SQLite数据库
- **处理阶段**: AI智能查询和数据分析
- **输出阶段**: 生成MD格式投资分析报告（仅本地存储）

### 与AI分身系统集成
- **触发方式**: 选择功能6：数据分析模块 / 直接提供复盘数据或图片
- **认知资产**: 投资洞察和模式的持续积累
- **跨对话支持**: 支持跨对话的投资认知资产管理

### 配置说明
- **数据库路径**: `5-数据分析/1-数据管理/trading_data.db`
- **备份策略**: 重要操作前自动备份
- **同步设置**: 专注本地数据管理，无外部依赖

## MCP集成服务