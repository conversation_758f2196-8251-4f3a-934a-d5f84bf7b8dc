# 6阶段数据分析工作流 - 快速索引和触发器
# 用于跨对话窗口的稳定触发和使用
# 版本：1.0
# 创建日期：2025-06-26

workflow_identity:
  name: "6阶段数据分析工作流"
  nickname: "小新的蜡笔数据分析流程"
  purpose: "标准化的投资复盘数据分析流程"
  design_inspiration: "参照RIPER-5严格确认机制"

# 快速触发关键词
trigger_keywords:
  primary: ["6阶段分析", "数据分析工作流", "复盘分析流程"]
  secondary: ["小新的蜡笔分析", "投资数据分析", "复盘数据处理"]
  context: ["上传复盘图片", "开始数据分析", "投资复盘"]

# 工作流状态追踪
current_stage_tracking:
  stage_indicator_format: "📍当前阶段：{stage_number}.{stage_name}"
  progress_format: "进度：{current_stage}/6 - {completion_percentage}%"
  checkpoint_format: "✅检查点：{criteria_met}/{total_criteria}"

# 阶段快速参考
stages_quick_reference:
  "1": 
    name: "数据录入"
    key_action: "图片识别+数据录入"
    checkpoint: "确认所有数据已录入完成"
    xiaoxin: "不参与"
  "2":
    name: "数据校对"
    key_action: "错误修正+格式完善"
    checkpoint: "数据准确性和格式标准化"
    xiaoxin: "不参与"
  "3":
    name: "数据验证"
    key_action: "三方对比+可信度评估"
    checkpoint: "验证分析完成"
    xiaoxin: "不参与"
  "4":
    name: "数据补充"
    key_action: "补充遗漏数据"
    checkpoint: "数据完整性达标"
    xiaoxin: "不参与"
  "5":
    name: "初步分析"
    key_action: "深度洞察生成"
    checkpoint: "分析质量达标"
    xiaoxin: "参与分析"
  "6":
    name: "最终复盘"
    key_action: "策略输出+经验总结"
    checkpoint: "复盘完整+策略可行"
    xiaoxin: "参与策略"

# 标准化确认模板
confirmation_templates:
  stage_completion_check: |
    🔍 阶段完成检查：
    当前阶段：{stage_number}.{stage_name}
    完成状态：
    {checkpoint_items}
    
    是否满足进入下一阶段的条件？
    - ✅ 是，进入下一阶段
    - 🔄 否，继续完善当前阶段
    - 📝 需要补充更多数据
  
  stage_transition_confirm: |
    🔄 阶段转换确认：
    ✅ 当前阶段：{current_stage} 已完成
    ➡️ 下一阶段：{next_stage}
    
    请确认：
    - "确认进入" - 开始下一阶段
    - "继续当前" - 继续完善当前阶段
    - "数据补充" - 需要补充数据
  
  xiaoxin_introduction: |
    🎨 小新的蜡笔登场：
    嗨！我是小新的蜡笔，你的投资分析学习伙伴！
    从这个阶段开始，我会和你一起进行分析，
    分享我的经验和建议。让我们一起挖掘数据背后的投资机会！

# 文件路径模板
file_path_templates:
  raw_input: "5-数据分析/原始录入/原始识别-{date}.md"
  analysis_output: "1-输入/2-碎片笔记/股市复盘-{date}.md"
  workflow_log: "0-辅助/workflow_execution_log.txt"

# 错误处理和恢复
error_handling:
  stage_confusion: "如果不确定当前阶段，检查最近的文件修改时间和内容"
  data_loss: "如果数据丢失，从原始识别文件重新开始"
  workflow_interruption: "如果工作流中断，询问用户当前阶段状态"
  
recovery_commands:
  check_current_stage: "检查当前工作流阶段"
  resume_workflow: "恢复工作流执行"
  restart_workflow: "重新开始工作流"

# 质量标准
quality_standards:
  data_accuracy: "数据识别准确率>95%"
  verification_completeness: "三方对比分析完整"
  analysis_depth: "洞察具有可操作性"
  strategy_practicality: "策略建议具体可行"

# 跨对话持久化机制
persistence_mechanism:
  stage_memory: "在每次对话开始时检查工作流状态"
  data_continuity: "确保数据在不同对话窗口间的连续性"
  context_restoration: "快速恢复工作流上下文"
  
context_check_commands:
  - "当前工作流状态如何？"
  - "我们进行到哪个阶段了？"
  - "继续之前的数据分析"
  - "恢复投资复盘分析"

# 使用说明
usage_instructions:
  start_workflow: "上传复盘图片或说'开始6阶段分析'"
  check_progress: "询问'当前进度如何？'"
  stage_transition: "等待系统确认提示，明确回复确认选项"
  xiaoxin_interaction: "在第5、6阶段与小新的蜡笔互动交流"
  
best_practices:
  - "每个阶段都要等待确认提示"
  - "不要跳过数据校对和验证"
  - "充分利用小新的蜡笔的分析经验"
  - "保持数据的真实性和完整性"

# 系统集成点
integration_points:
  persona_yaml: "通过investment_analysis_system配置调用"
  ai_avatar_menu: "功能6：小新的蜡笔"
  cognitive_modules: "存储在investment-analysis-system.yaml中"
  time_mcp: "确保时间戳准确性"

# 版本更新日志
changelog:
  v1.0: "初始版本，建立6阶段工作流标准化流程"

# 引用说明
# 在任何对话中，AI助手应该：
# 1. 识别到投资复盘相关需求时，自动调用此工作流
# 2. 严格按照6阶段顺序执行，不跳过任何阶段
# 3. 每个阶段结束都要进行确认
# 4. 在第5、6阶段激活小新的蜡笔角色
# 5. 保持跨对话的工作流连续性
