# 原始录入目录

## 📋 目录说明
这里存放AI识别图片后生成的原始数据，经过用户验证修正。

## 📁 文件命名规范
- 格式：`YYYY-MM-DD-原始识别.md`
- 示例：`2025-06-26-原始识别.md`

## ✅ 数据验证流程
1. AI识别图片生成原始文件
2. 用户查看并修正识别错误
3. 标记验证状态为"✅ 已验证修正"

## 📊 标准数据格式
每个文件包含：
- 基础市场数据
- 涨跌停统计
- 龙虎榜数据
- 空间梯队
- 人气排名数据（今晚收盘后 + 明早开盘前，精确到分钟）

## ⏰ 时间标准化
- 所有时间信息通过Time MCP获取准确的北京时间
- 人气排名时间精确到分钟：YYYY年MM月DD日 HH:MM
- 确保数据时间戳的准确性和一致性

## 🔄 工作流程
### 每日数据录入
1. **前一晚**：录入今晚人气排名到前一日的原始识别.md
2. **当日早上**：录入明早人气排名到当日的原始识别.md
3. **异动分析**：AI基于两天数据生成异动分析，放入当日文件顶部
4. **复盘数据**：继续录入当日的基础市场数据、龙虎榜等

### 数据分析
验证后的数据将用于AI分析，生成碎片笔记中的深度复盘分析。

### 人气异动分析逻辑
- **大幅上升**：排名提升20位以上（重点关注）
- **中等上升**：排名提升10-20位（次要关注）
- **新进榜单**：昨晚未上榜，今早进入前50
- **开盘决策**：基于异动分析提供操作建议
