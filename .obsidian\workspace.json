{"main": {"id": "324e2b7a0a2aed88", "type": "split", "children": [{"id": "d5339a75597fd04a", "type": "tabs", "children": [{"id": "52d5eacbb002dc9b", "type": "leaf", "state": {"type": "thino_view", "state": {}, "icon": "Memos", "title": "<PERSON><PERSON>"}}, {"id": "3fd10fb03575423b", "type": "leaf", "state": {"type": "markdown", "state": {"file": "5-数据分析/原始录入/原始识别-2025-06-27.md", "mode": "source", "source": false}, "icon": "lucide-file", "title": "原始识别-2025-06-27"}}], "currentTab": 1}], "direction": "vertical"}, "left": {"id": "9f3c145813b44c60", "type": "split", "children": [{"id": "f957f74ea5e4fa9a", "type": "tabs", "children": [{"id": "dd8c72fc438882f6", "type": "leaf", "state": {"type": "file-explorer", "state": {"sortOrder": "alphabetical", "autoReveal": false}, "icon": "lucide-folder-closed", "title": "文件列表"}}, {"id": "da0db952287911d9", "type": "leaf", "state": {"type": "search", "state": {"query": "", "matchingCase": false, "explainSearch": false, "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical"}, "icon": "lucide-search", "title": "搜索"}}, {"id": "6ed2f1edbbaa8377", "type": "leaf", "state": {"type": "bookmarks", "state": {}, "icon": "lucide-bookmark", "title": "书签"}}, {"id": "783440871e4b4b21", "type": "leaf", "pinned": true, "state": {"type": "markdown", "state": {"file": "3-任务/Dashboard.md", "mode": "preview", "source": true}, "pinned": true, "icon": "lucide-file", "title": "Dashboard"}}]}], "direction": "horizontal", "width": 400, "collapsed": true}, "right": {"id": "128c55eacc93ffde", "type": "split", "children": [{"id": "a0ce31a991c18e01", "type": "tabs", "children": [{"id": "55ef978f5cc6ed6f", "type": "leaf", "state": {"type": "backlink", "state": {"file": "5-数据分析/原始录入/原始识别-2025-06-27.md", "collapseAll": false, "extraContext": false, "sortOrder": "alphabetical", "showSearch": false, "searchQuery": "", "backlinkCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-coming-in", "title": "原始识别-2025-06-27 的反向链接列表"}}, {"id": "fd7dcdd756b8fe14", "type": "leaf", "state": {"type": "outgoing-link", "state": {"file": "5-数据分析/原始录入/原始识别-2025-06-27.md", "linksCollapsed": false, "unlinkedCollapsed": true}, "icon": "links-going-out", "title": "原始识别-2025-06-27 的出链列表"}}, {"id": "6fd3f438d60509c5", "type": "leaf", "state": {"type": "tag", "state": {"sortOrder": "frequency", "useHierarchy": true, "showSearch": false, "searchQuery": ""}, "icon": "lucide-tags", "title": "标签"}}, {"id": "8078386359a20deb", "type": "leaf", "state": {"type": "outline", "state": {"file": "5-数据分析/原始录入/原始识别-2025-06-27.md", "followCursor": false, "showSearch": false, "searchQuery": ""}, "icon": "lucide-list", "title": "原始识别-2025-06-27 的大纲"}}, {"id": "966bc8776aaa2e58", "type": "leaf", "state": {"type": "all-properties", "state": {"sortOrder": "frequency"}, "icon": "lucide-file", "title": "插件不再活动"}}]}], "direction": "horizontal", "width": 300, "collapsed": true}, "left-ribbon": {"hiddenItems": {"float-search:Search obsidian in modal view": false, "obsidian-memos:Thino": false, "table-editor-obsidian:Advanced Tables Toolbar": false, "workspaces:管理工作区布局": false, "switcher:打开快速切换": false, "graph:查看关系图谱": false, "canvas:新建白板": false, "daily-notes:打开/创建今天的日记": false, "templates:插入模板": false, "command-palette:打开命令面板": false, "templater-obsidian:Templater": false, "homepage:Open homepage": false, "obsidian-excalidraw-plugin:新建绘图文件": false}}, "active": "3fd10fb03575423b", "lastOpenFiles": ["3-任务/Dashboard.md", "Excalidraw/重要与紧急.md", "5-数据分析/原始录入/原始识别-2025-06-27.md", "5-数据分析/资金追踪记录.md", "5-数据分析/__pycache__/成交量对比工具.cpython-313.pyc", "5-数据分析/__pycache__", "3-任务/2025年第25周-MVP决策板.md", "3-任务/考研督导系统/markdown_storage/templates/quick_log_template.md", "3-任务/日记/日记-2024-01-04.md", "5-数据分析/原始录入/原始识别-2025-06-26.md", "1-输入/2-碎片笔记/股市复盘-2025-06-26.md", "5-数据分析/初步分析/初步分析-2025-06-26.md", "5-数据分析/初步分析", "Excalidraw/快乐与意义.md", "check_data_inline.py", "5-数据分析/1-数据管理/quick_check_20250626.py", "5-数据分析/1-数据管理", "Excalidraw/Drawing 2025-06-27 10.47.53.excalidraw.md", "0-辅助/workflow_execution_log.txt", "0-辅助/cognitive-modules/six-stage-workflow-index.yaml", "3-任务/日记/日记-2025-06-27.md", "2-输出/1-闪念笔记/闪念-2025-06-27.md", "3-任务/日记/日记-2025-06-26.md", "2-输出/1-闪念笔记/闪念-2025-06-25.md", "5-数据分析/成交量对比工具.py", "5-数据分析/数据分层处理执行流程.md", "0-辅助/xiaoxin-investment-learning-archive.md", "0-辅助/cognitive-modules/xiaoxin-crayon-system.yaml", "2-输出/5-AI-Diary/2025年6月10日 读取档案并调用思考搭档.md", "5-数据分析/原始录入/模板-原始识别.md", "5-数据分析/原始录入/README.md", "2-输出/5-AI-Diary/2025年6月2日.md", "2-输出/5-AI-Diary/2025年6月1日.md", "1-输入/2-碎片笔记/2024年3月2日 直播记录.md", "1-输入/2-碎片笔记/2024年2月20日 复盘.md", "1-输入/2-碎片笔记/2024年3月12日 复盘笔记.md", "0-辅助/Attachment/Pasted image 20250625115626.png", "0-辅助/Attachment/Pasted image 20250625115043.png", "0-辅助/Attachment/3c83a616e9f4c2d683adaff355b5df2.jpg", "0-辅助/Attachment/04fc54448b358ed570ebad49b6a25f4 1.png", "0-辅助/Attachment/Pasted image 20250625111237.png", "0-辅助/Attachment/Pasted image 20250625095925.png", "0-辅助/Attachment/04fc54448b358ed570ebad49b6a25f4.png", "0-辅助/Attachment/f95914d76bef4c5609144cd1704afbd.jpg", "0-辅助/Attachment/fc3fe12d8bedcde0de4959ba443ff90.png", "0-辅助/Attachment/Pasted image 20250614071834.png", "6-可视化项目/AI自动化的应用.canvas", "工作梳理.canvas", "未命名.canvas", "AI自动化的应用.canvas"]}