# 分层认知资产管理系统
# AI分身系统核心功能模块
# 版本：1.0
# 创建日期：2025-01-27

system_overview:
  name: "分层认知资产管理系统"
  description: "AI分身系统的核心功能，实现认知资产的智能分层存储和显性调用"
  version: "1.0"
  core_principle: "在任何对话中都必须执行的核心功能机制"

architecture_layers:
  ai_avatar_system_foundation:
    description: "AI分身系统底层架构"
    components:
      persona_yaml:
        purpose: "核心设计原则、通用哲学、系统级机制"
        storage_criteria:
          - "跨领域通用的设计原则"
          - "长期稳定的系统哲学"
          - "底层架构原则"
          - "系统级触发机制"
        examples:
          - "反庞氏原则"
          - "文件操作原子性原则"
          - "AI协作哲学"
          - "深呼吸行为模式"
      
      memory:
        purpose: "动态对话洞察、临时认知资产、跨对话经验"
        storage_criteria:
          - "阶段性认知洞察"
          - "具体对话经验"
          - "可更新的临时认知"
          - "动态模式识别"
        examples:
          - "特定对话中的洞察"
          - "用户偏好记录"
          - "临时性操作经验"
          - "阶段性认知突破"
      
      readme:
        purpose: "系统全貌展示，每次更新后自动同步"
        sync_rule: "任何persona.yaml或cognitive-modules更新后都要同步"
        content_type:
          - "功能总览"
          - "使用指南"
          - "系统架构说明"
          - "快速参考"

  functional_modules:
    description: "基于底层架构生长的功能模块"
    cognitive_modules:
      purpose: "专业领域模块（投资分析等）"
      auto_routing_rule: "投资相关内容直接放入cognitive-modules"
      storage_criteria:
        - "特定领域的专业框架"
        - "完整的分析体系"
        - "详细的操作配置"
        - "领域专用知识"

core_distinction_logic:
  primary_decision: "persona.yaml vs Memory"
  decision_criteria:
    persona_yaml_indicators:
      - "底层架构原则"
      - "长期稳定哲学"
      - "跨领域通用性"
      - "系统级机制"
    
    memory_indicators:
      - "动态对话洞察"
      - "阶段性认知"
      - "具体经验记录"
      - "临时性发现"
  
  auto_routing_rules:
    investment_content: "直接进入cognitive-modules/investment-*"
    system_architecture: "评估后进入persona.yaml或cognitive-modules"
    user_preferences: "进入Memory"
    operational_experience: "进入Memory"

explicit_confirmation_mechanism:
  active_identification:
    trigger_format: |
      🧠 认知资产确认：
      我识别到：[具体内容]
      建议存储位置：[persona.yaml/Memory/cognitive-modules]
      理由：[具体判断依据]
      是否确认纳入？
    
    trigger_conditions:
      - "出现新的设计原则或哲学"
      - "形成可复用的思维框架"
      - "发现重要的系统洞察"
      - "建立新的操作机制"
  
  secondary_confirmation:
    trigger_format: |
      🤔 存储位置确认：
      这条认知资产：[具体内容]
      我倾向于放入：[位置A]，但也可能适合：[位置B]
      您的判断是？
    
    trigger_conditions:
      - "边界模糊的认知资产"
      - "具有多重属性的内容"
      - "不确定通用性程度"
  
  explicit_invocation:
    display_format: |
      📚 调用认知资产：
      - 来源：[persona.yaml/Memory/cognitive-modules]
      - 内容：[具体调用的原则/经验/框架]
      - 应用：[如何指导当前回应]
    
    invocation_scenarios:
      - "回应用户问题时"
      - "进行系统分析时"
      - "执行特定功能时"
      - "提供建议时"

implementation_requirements:
  mandatory_execution: "在任何对话中都必须执行此机制"
  consistency_principle: "确保跨对话窗口的一致性"
  transparency_principle: "所有认知资产的使用都要显性化"
  user_control_principle: "用户拥有最终决策权"

quality_control:
  avoid_duplication: "防止在不同层级重复存储相同内容"
  maintain_coherence: "确保各层级间的逻辑一致性"
  enable_evolution: "支持认知资产的动态更新和优化"
  ensure_accessibility: "确保认知资产能被有效调用"

integration_points:
  startup_sequence: "AI分身系统启动时自动加载此机制"
  dialogue_monitoring: "对话过程中持续监测认知资产生成"
  function_execution: "执行任何功能时都要显性调用相关认知资产"
  system_updates: "系统更新时确保机制的一致性维护"

success_metrics:
  user_awareness: "用户清楚了解认知资产的存储和调用"
  system_transparency: "AI的认知资产使用完全透明"
  knowledge_accumulation: "认知资产持续积累和优化"
  cross_dialogue_consistency: "跨对话的系统行为一致性" 