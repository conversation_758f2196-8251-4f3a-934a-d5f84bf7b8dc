# MD文件数据管理系统配置

## 系统概述
基于数据分层处理理念的投资复盘数据管理系统，实现原始数据验证+AI智能分析的双层模式。

## 核心理念
- **数据透明性**：所有数据完全可视，用户可直接验证
- **分层处理**：原始录入 + 智能分析两个独立阶段
- **时间精确性**：通过Time MCP确保时间戳准确性
- **模板同步**：版本控制机制支持动态结构调整

## 文件结构
```
5-数据分析/原始录入/
├── YYYY-MM-DD-原始识别.md    # 每日原始数据
├── 模板-原始识别.md           # 标准格式模板
└── README.md                 # 工作流程说明

1-输入/2-碎片笔记/
└── 股市复盘-YYYY-MM-DD.md    # AI分析后的深度洞察
```

## 工作流程
### 阶段1：原始数据录入
1. **前一晚**：录入今晚人气排名到前一日原始识别.md
2. **当日早上**：录入明早人气排名到当日原始识别.md
3. **异动分析**：AI生成异动分析放入碎片笔记/股市复盘-日期.md顶部
4. **收盘后**：录入基础市场数据到当日原始识别.md

### 阶段2：智能分析
- AI读取多天原始识别.md数据
- 生成深度分析到碎片笔记/股市复盘-日期.md
- 包含趋势分析、模式识别、投资建议

## 数据结构标准
### 原始识别.md包含模块
1. **🔥 人气异动分析**（开盘前，文件顶部）
2. **📊 基础市场数据**
3. **📈 涨跌停统计**
4. **🎯 辨识度个股**
5. **📋 一字板个股**
6. **💰 龙虎榜数据**
7. **🪜 空间梯队**
8. **🔥 人气排名数据**（精确到分钟时间戳）

### 人气异动分析逻辑
- **大幅上升**：排名提升20位以上（重点关注）
- **中等上升**：排名提升10-20位（次要关注）
- **新进榜单**：昨晚未上榜，今早进入前50
- **开盘决策**：基于异动分析提供操作建议

## 模板同步机制
### 版本控制
- 模板文件包含版本号和更新日志
- AI自动检测最新模板版本
- 历史文件保持原格式，新文件使用新格式

### 动态调整支持
- 支持新增/删除数据模块
- 支持调整表格结构
- 支持修改分析维度

## 时间标准化
- 所有时间信息通过Time MCP获取
- 人气排名时间精确到分钟：YYYY年MM月DD日 HH:MM
- 录入时间、验证时间统一格式

## 触发关键词
- "数据分析模块"
- "投资分析"
- "复盘数据"
- "数据录入"
- "原始识别"
- "人气异动"
- "图片识别"
- "复盘图片"

## 使用示例
### 数据录入
"帮我识别这张复盘图片，生成2025-06-26-原始识别.md"

### 异动分析
"基于昨晚和今早的人气排名数据，生成异动分析到股市复盘-2025-06-26.md"

### 深度分析
"基于最近5天的原始识别数据，生成本周复盘分析"

## 系统优势
- **完全透明**：用户可直接查看所有识别结果
- **易于修正**：发现错误立即编辑文件
- **无工具依赖**：不需要Python脚本或数据库
- **版本可控**：支持动态结构调整
- **时间精确**：确保数据时间戳准确性

## 集成点
- **AI分身系统菜单**：功能6 - 投资数据管理
- **persona.yaml**：数据分层处理理念、MD文件数据管理系统
- **Time MCP**：时间标准化服务
- **碎片笔记系统**：深度分析输出

---
**配置版本**：v1.0
**创建时间**：2025-06-26
**最后更新**：2025-06-26
