#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
成交量对比工具 - 小新的蜡笔专用
自动抓取昨日成交量数据，生成对比分析

功能：
1. 从历史原始识别.md文件中提取昨日成交量
2. 计算成交量变化幅度
3. 生成对比分析文本
4. 自动更新今日的原始识别.md文件
"""

import os
import re
from datetime import datetime, timedelta
from pathlib import Path

class VolumeComparator:
    def __init__(self, data_dir="5-数据分析/原始录入"):
        """
        初始化成交量对比工具
        
        Args:
            data_dir: 数据目录路径
        """
        self.data_dir = Path(data_dir)
        self.today = datetime.now().strftime("%Y-%m-%d")
        self.yesterday = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")
    
    def extract_volume_from_file(self, file_path):
        """
        从原始识别.md文件中提取成交量数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            int: 成交量（亿元），如果未找到返回None
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 匹配成交量数据的正则表达式
            # 支持格式：成交量：15832亿 或 成交量：1.58万亿
            volume_patterns = [
                r'成交量[：:]\s*(\d+)亿',  # 直接亿为单位
                r'成交量[：:]\s*(\d+\.?\d*)万亿',  # 万亿为单位
                r'成交量[：:]\s*(\d+\.?\d*)\s*万亿',  # 万亿为单位（带空格）
            ]
            
            for pattern in volume_patterns:
                match = re.search(pattern, content)
                if match:
                    volume_str = match.group(1)
                    if '万亿' in pattern:
                        # 万亿转换为亿
                        return int(float(volume_str) * 10000)
                    else:
                        # 直接是亿
                        return int(volume_str)
            
            return None
            
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            return None
    
    def get_yesterday_volume(self):
        """
        获取昨日成交量数据
        
        Returns:
            int: 昨日成交量（亿元），如果未找到返回None
        """
        yesterday_file = self.data_dir / f"原始识别-{self.yesterday}.md"
        
        if not yesterday_file.exists():
            print(f"昨日数据文件不存在: {yesterday_file}")
            return None
        
        return self.extract_volume_from_file(yesterday_file)
    
    def calculate_volume_change(self, today_volume, yesterday_volume):
        """
        计算成交量变化
        
        Args:
            today_volume: 今日成交量（亿元）
            yesterday_volume: 昨日成交量（亿元）
            
        Returns:
            dict: 包含变化金额、变化幅度、趋势分析的字典
        """
        if yesterday_volume is None or yesterday_volume == 0:
            return {
                'change_amount': 0,
                'change_percent': 0,
                'trend_analysis': '无昨日数据对比'
            }
        
        change_amount = today_volume - yesterday_volume
        change_percent = (change_amount / yesterday_volume) * 100
        
        # 趋势分析
        if change_percent > 20:
            trend = "大幅放量"
        elif change_percent > 10:
            trend = "明显放量"
        elif change_percent > 0:
            trend = "温和放量"
        elif change_percent > -10:
            trend = "温和缩量"
        elif change_percent > -20:
            trend = "明显缩量"
        else:
            trend = "大幅缩量"
        
        return {
            'change_amount': change_amount,
            'change_percent': change_percent,
            'trend_analysis': trend
        }
    
    def generate_comparison_text(self, today_volume, yesterday_volume=None):
        """
        生成成交量对比分析文本
        
        Args:
            today_volume: 今日成交量（亿元）
            yesterday_volume: 昨日成交量（亿元），可选
            
        Returns:
            str: 格式化的对比分析文本
        """
        if yesterday_volume is None:
            yesterday_volume = self.get_yesterday_volume()
        
        if yesterday_volume is None:
            return f"- 成交量：{today_volume}亿（昨日数据缺失，无法对比）"
        
        comparison = self.calculate_volume_change(today_volume, yesterday_volume)
        
        change_text = ""
        if comparison['change_amount'] > 0:
            change_text = f"较昨日增加{comparison['change_amount']}亿（+{comparison['change_percent']:.1f}%）"
        elif comparison['change_amount'] < 0:
            change_text = f"较昨日减少{abs(comparison['change_amount'])}亿（{comparison['change_percent']:.1f}%）"
        else:
            change_text = "与昨日持平"
        
        return f"""- 成交量：{today_volume}亿，{change_text}
- 量能分析：{comparison['trend_analysis']}
- 昨日对比：{yesterday_volume}亿 → {today_volume}亿"""

def main():
    """
    主函数 - 演示用法
    """
    comparator = VolumeComparator()
    
    # 示例：今日成交量15832亿
    today_volume = 15832
    comparison_text = comparator.generate_comparison_text(today_volume)
    
    print("成交量对比分析：")
    print(comparison_text)

if __name__ == "__main__":
    main()
