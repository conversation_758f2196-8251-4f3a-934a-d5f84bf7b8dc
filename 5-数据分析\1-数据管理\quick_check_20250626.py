#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速检查2025年6月26日数据完整性
"""

import sqlite3
import os

def check_data_completeness():
    """检查2025年6月26日数据完整性"""
    
    # 数据库路径
    db_path = "trading_data.db"
    
    if not os.path.exists(db_path):
        print("❌ 数据库文件不存在")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        target_date = "2025-06-26"
        print(f"📊 检查 {target_date} 数据完整性")
        print("=" * 50)
        
        # 检查各表数据
        tables = [
            ("daily_review", "日度复盘"),
            ("sector_analysis", "板块分析"), 
            ("dragon_tiger_list", "龙虎榜"),
            ("space_ladder", "空间梯队"),
            ("distinctive_stocks", "辨识度个股"),
            ("one_word_boards", "一字板统计")
        ]
        
        total_records = 0
        missing_tables = []
        
        for table_name, table_desc in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name} WHERE date = ?", (target_date,))
                count = cursor.fetchone()[0]
                
                if count > 0:
                    print(f"✅ {table_desc}: {count} 条记录")
                    total_records += count
                else:
                    print(f"❌ {table_desc}: 无数据")
                    missing_tables.append(table_desc)
                    
            except sqlite3.Error as e:
                print(f"❌ {table_desc}: 表不存在或查询错误 - {e}")
                missing_tables.append(table_desc)
        
        print("=" * 50)
        print(f"📈 总记录数: {total_records}")
        
        if missing_tables:
            print(f"⚠️  缺失数据表: {', '.join(missing_tables)}")
            print(f"📊 数据完整性: {((6-len(missing_tables))/6)*100:.1f}%")
        else:
            print("✅ 所有表都有数据")
            print("📊 数据完整性: 100%")
            
        # 显示需要补充的数据类型
        if missing_tables:
            print("\n🔧 需要补充的数据类型:")
            for table in missing_tables:
                print(f"   - {table}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 检查过程出错: {e}")

if __name__ == "__main__":
    check_data_completeness()
