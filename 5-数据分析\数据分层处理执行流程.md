# 数据分层处理执行流程 - 小新的蜡笔专用

## 🚨 重要提醒：每次数据录入必须遵循此流程！

### 核心理念
**数据分层处理理念**：建立原始数据验证+AI智能分析生成洞察的双层数据管理模式

### 📋 标准执行流程

#### 🔴 第一步：原始识别.md（数据验证）
**位置**：`5-数据分析/原始录入/原始识别-YYYY-MM-DD.md`

**操作要求**：
1. ✅ 只录入图片中明确显示的数据
2. ✅ 缺失数据标注"图片中无此信息"
3. ✅ 不编造或推测任何数据
4. ✅ 按照图片样式一比一记录
5. ✅ 精准记录数字，不进行省略和精简

**数据录入原则**：
- 成交量：精确记录（如15832亿，不写1.58万亿）
- 龙虎榜：完整录入所有显示的游资数据
- 空间梯队：按图片表格样式完整记录
- 同花顺点评：完整录入，不省略

#### 🟡 第二步：用户验证修正
**操作**：用户检查原始识别.md的准确性
- 修正识别错误
- 补充遗漏信息
- 标记验证状态为"✅ 已验证修正"

#### 🟢 第三步：AI分析生成洞察
**位置**：`1-输入/2-碎片笔记/股市复盘-YYYY-MM-DD.md`
**操作**：基于验证后的原始数据进行深度分析

### ⚠️ 禁止操作
- ❌ 直接跳到分析阶段
- ❌ 省略原始数据验证步骤
- ❌ 编造或推测数据
- ❌ 简化或精简重要信息

### 🔧 触发机制
当用户提供复盘图片时，AI必须：
1. 首先询问："我需要先创建原始识别.md进行数据验证，还是直接分析？"
2. 如果用户未明确，默认执行第一步：创建原始识别.md
3. 提醒用户验证数据准确性

### 📁 文件结构
```
5-数据分析/原始录入/
├── 原始识别-2025-06-26.md    # 每日原始数据
├── 模板-原始识别.md           # 标准格式模板
└── README.md                 # 工作流程说明

1-输入/2-碎片笔记/
└── 股市复盘-2025-06-26.md    # AI分析后的深度洞察
```

### 🎯 核心价值
- **数据真实性**：确保所有分析基于真实可验证的数据
- **透明可控**：用户可直接查看和修正所有识别结果
- **分析价值**：基于可信数据生成有价值的认知资产

---
**创建时间**：2025-06-26 18:00:00
**适用场景**：股市复盘数据管理、投资分析、任何需要数据验证的场景
**更新记录**：v1.0 初始版本，解决AI跳过数据验证步骤的问题
