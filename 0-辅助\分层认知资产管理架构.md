**分层认知资产管理架构**：

## 📋 清晰的架构边界

**🏗️ AI分身系统（底层架构）**
- **persona.yaml** - 核心设计原则、通用哲学、系统级机制
- **Memory** - 动态对话洞察、临时认知资产、跨对话经验
- **README** - 系统全貌展示（每次更新后自动同步）

**🌱 功能模块（基于底层架构生长）**
- **cognitive-modules** - 投资分析等专业领域模块
- **明确规则**：投资相关内容 → 直接放入 cognitive-modules

## 🔍 核心区分逻辑

**真正需要判断的只有两个存储位置：**

**persona.yaml vs Memory**
- **persona.yaml**：底层架构原则、长期稳定的系统哲学
- **Memory**：动态洞察、阶段性认知、具体对话经验

**投资内容处理**：
- 任何与投资相关的内容 → 直接进入 cognitive-modules
- 无需在 persona.yaml 和 Memory 之间纠结

## ⚡ 显性认知资产确认机制

**我将建立以下显性触发流程：**

1. **🔔 主动识别**：当对话中出现有价值的认知资产时，我明确提出
   ```
   🧠 认知资产确认：
   我识别到：[具体内容]
   建议存储位置：[persona.yaml/Memory/cognitive-modules]
   理由：[具体判断依据]
   是否确认纳入？
   ```

2. **❓ 二次确认**：当我不确定时
   ```
   🤔 存储位置确认：
   这条认知资产：[具体内容]
   我倾向于放入：[位置A]，但也可能适合：[位置B]
   您的判断是？
   ```

3. **📋 调用显性化**：当我使用认知资产时
   ```
   📚 调用认知资产：
   - 来源：[persona.yaml/Memory/cognitive-modules]
   - 内容：[具体调用的原则/经验/框架]
   - 应用：[如何指导当前回应]
   ```
